"""
监控数据模型
包含数据聚合相关的模型定义
"""

from tortoise import fields
from app.models.system.utils import BaseModel, TimestampMixin


class MonitorHourlyStats(BaseModel, TimestampMixin):
    """小时级监控统计数据"""
    id = fields.IntField(pk=True, description="统计ID")
    date_hour = fields.DatetimeField(description="小时时间点")
    total_requests = fields.IntField(default=0, description="总请求数")
    avg_response_time = fields.FloatField(default=0, description="平均响应时间")
    max_response_time = fields.FloatField(default=0, description="最大响应时间")
    min_response_time = fields.FloatField(default=0, description="最小响应时间")
    error_count = fields.IntField(default=0, description="错误数量")
    strm_tasks_created = fields.IntField(default=0, description="创建的STRM任务数")
    strm_tasks_completed = fields.IntField(default=0, description="完成的STRM任务数")
    strm_tasks_failed = fields.IntField(default=0, description="失败的STRM任务数")

    class Meta:
        table = "monitor_hourly_stats"
        table_description = "小时级监控统计"
        default_connection = "conn_system"
        indexes = [
            ("date_hour",),
            ("date_hour", "total_requests"),
        ]


class MonitorDailyStats(BaseModel, TimestampMixin):
    """日级监控统计数据"""
    id = fields.IntField(pk=True, description="统计ID")
    date = fields.DateField(description="日期")
    total_requests = fields.IntField(default=0, description="总请求数")
    avg_response_time = fields.FloatField(default=0, description="平均响应时间")
    max_response_time = fields.FloatField(default=0, description="最大响应时间")
    min_response_time = fields.FloatField(default=0, description="最小响应时间")
    total_errors = fields.IntField(default=0, description="总错误数")
    strm_tasks_total = fields.IntField(default=0, description="STRM任务总数")
    strm_tasks_completed = fields.IntField(default=0, description="完成的STRM任务数")
    strm_tasks_failed = fields.IntField(default=0, description="失败的STRM任务数")
    strm_tasks_success_rate = fields.FloatField(default=0, description="STRM任务成功率")

    class Meta:
        table = "monitor_daily_stats"
        table_description = "日级监控统计"
        default_connection = "conn_system"
        indexes = [
            ("date",),
            ("date", "total_requests"),
        ]


__all__ = [
    "MonitorHourlyStats",
    "MonitorDailyStats"
]
