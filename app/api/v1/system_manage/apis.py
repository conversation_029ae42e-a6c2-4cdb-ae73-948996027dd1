"""
系统API管理
"""

from fastapi import APIRouter, Query, Body
from fastapi.params import Depends
from tortoise.expressions import Q

from app.api.v1.utils import refresh_api_list, insert_log, generate_tags_recursive_list
from app.controllers import user_controller
from app.controllers.api import api_controller
from app.core.ctx import CTX_USER_ID
from app.models.system import Api, Role, User
from app.models.system import LogType, LogDetailType
from app.schemas.apis import ApiCreate, ApiUpdate, ApiSearch
from app.schemas.base import Success, SuccessExtra
from app.core.dependency import DependPermission, get_current_user

router = APIRouter()
router_api = APIRouter(tags=["系统管理-API"])


@router.post("/apis/all/", summary="查看API列表")
async def _(obj_in: ApiSearch):
    q = Q()
    if obj_in.api_path:
        q &= Q(api_path__contains=obj_in.api_path)
    if obj_in.summary:
        q &= Q(summary=obj_in.summary)
    if obj_in.tags:
        for tag in obj_in.tags:
            q &= Q(tags__contains=[tag])
    if obj_in.status_type:
        q &= Q(status_type=obj_in.status_type)

    user_id = CTX_USER_ID.get()
    user_obj = await user_controller.get(id=user_id)
    await user_obj.fetch_related("by_user_roles")
    user_role_objs: list[Role] = await user_obj.by_user_roles
    user_role_codes = [role_obj.role_code for role_obj in user_role_objs]
    if "R_SUPER" in user_role_codes:
        total, api_objs = await api_controller.list(
            page=obj_in.current, page_size=obj_in.size, search=q, order=["tags", "id"]
        )
    else:
        # 优化：使用单次查询获取所有相关API，避免循环查询
        role_ids = [role.id for role in user_role_objs]

        # 构建查询，获取用户角色关联的所有API
        api_query = Api.filter(by_api_roles__id__in=role_ids).filter(q).distinct()

        # 获取总数
        total = await api_query.count()

        # 分页查询
        start = (obj_in.current - 1) * obj_in.size
        api_objs = await api_query.offset(start).limit(obj_in.size).order_by("tags", "id")

    records = []
    for obj in api_objs:
        data = await obj.to_dict(exclude_fields=["create_time", "update_time"])
        records.append(data)
    data = {"records": records}
    await insert_log(log_type=LogType.UserLog, log_detail_type=LogDetailType.ApiGetList, by_user_id=user_obj.id)
    return SuccessExtra(data=data, total=total, current=obj_in.current, size=obj_in.size)


@router.get("/apis/{api_id}", summary="查看API")
async def _(api_id: int):
    api_obj = await api_controller.get(id=api_id)
    data = await api_obj.to_dict(exclude_fields=["id", "create_time", "update_time"])
    await insert_log(log_type=LogType.UserLog, log_detail_type=LogDetailType.ApiGetOne, by_user_id=0)
    return Success(data=data)


def build_api_tree(apis: list[Api]):
    parent_map = {"root": {"id": "root", "children": []}}
    # 遍历输入数据
    for api in apis:
        tags = api.tags
        parent_id = "root"
        for tag in tags:
            node_id = f"parent${tag}"
            # 如果当前节点不存在，则创建一个新的节点
            if node_id not in parent_map:
                node = {"id": node_id, "summary": tag, "children": []}
                parent_map[node_id] = node
                parent_map[parent_id]["children"].append(node)
            parent_id = node_id
        parent_map[parent_id]["children"].append(
            {
                "id": api.id,
                "summary": api.summary,
            }
        )
    return parent_map["root"]["children"]


@router.get("/apis/tree/", summary="查看API树")
async def _():
    api_objs = await Api.all()
    data = []
    if api_objs:
        data = build_api_tree(api_objs)
    await insert_log(log_type=LogType.UserLog, log_detail_type=LogDetailType.ApiGetTree, by_user_id=0)
    return Success(data=data)


@router.post("/apis", summary="创建API")
async def _(api_in: ApiCreate):
    if isinstance(api_in.tags, str):
        api_in.tags = api_in.tags.split("|")
    new_api = await api_controller.create(obj_in=api_in)
    await insert_log(log_type=LogType.UserLog, log_detail_type=LogDetailType.ApiCreateOne, by_user_id=0)
    return Success(msg="Created Successfully", data={"created_id": new_api.id})


@router.patch("/apis/{api_id}", summary="更新API")
async def _(api_id: int, api_in: ApiUpdate):
    if isinstance(api_in.tags, str):
        api_in.tags = api_in.tags.split("|")
    await api_controller.update(id=api_id, obj_in=api_in)
    await insert_log(log_type=LogType.UserLog, log_detail_type=LogDetailType.ApiUpdateOne, by_user_id=0)
    return Success(msg="Update Successfully", data={"updated_id": api_id})


@router.delete("/apis/{api_id}", summary="删除API")
async def _(api_id: int):
    await api_controller.remove(id=api_id)
    await insert_log(log_type=LogType.UserLog, log_detail_type=LogDetailType.ApiDeleteOne, by_user_id=0)
    return Success(msg="Deleted Successfully", data={"deleted_id": api_id})


@router.delete("/apis", summary="批量删除API")
async def _(ids: str = Query(..., description="API ID列表, 用逗号隔开")):
    api_ids = ids.split(",")
    deleted_ids = []
    for api_id in api_ids:
        api_obj = await Api.get(id=int(api_id))
        await api_obj.delete()
        deleted_ids.append(int(api_id))
    await insert_log(log_type=LogType.UserLog, log_detail_type=LogDetailType.ApiBatchDelete, by_user_id=0)
    return Success(msg="Deleted Successfully", data={"deleted_ids": deleted_ids})


@router.post("/apis/refresh/", summary="刷新API列表")
async def _():
    await refresh_api_list()
    await insert_log(log_type=LogType.UserLog, log_detail_type=LogDetailType.ApiRefresh, by_user_id=0)
    return Success()


@router.post("/apis/tags/all/", summary="查看API tags")
async def _():
    data = await generate_tags_recursive_list()
    # await insert_log(log_type=LogType.UserLog, log_detail_type=LogDetailType.ApiRefresh, by_user_id=0)
    return Success(data=data)


@router_api.get("/list", summary="获取API列表")
async def apis_list(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
    page: int = Query(1, description="页码", gt=0),
    limit: int = Query(10, description="每页数量", gt=0),
    api_name: str = Query("", description="API名称"),
    api_path: str = Query("", description="API路径"),
    is_auth: int = Query(None, description="是否需要认证"),
):
    data = await api_controller.get_apis(page=page, limit=limit, api_name=api_name, api_path=api_path, is_auth=is_auth)
    return Success(data=data)


@router_api.post("/update", summary="修改API")
async def apis_update(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
    api_id: int = Body(..., embed=True),
    api_desc: str = Body(None, embed=True),
    is_auth: int = Body(None, embed=True),
):
    data = await api_controller.update_api(api_id=api_id, api_desc=api_desc, is_auth=is_auth)
    return Success(data=data)


@router_api.get("/refresh", summary="刷新API列表")
async def apis_refresh(
    current_user: User = Depends(get_current_user),
    _: None = DependPermission,
):
    from app.api.v1.utils import refresh_api_list

    await refresh_api_list()
    return Success()





