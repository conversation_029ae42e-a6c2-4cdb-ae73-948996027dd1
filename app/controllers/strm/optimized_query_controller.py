"""
优化的查询控制器
解决N+1查询问题，提升查询性能
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from tortoise.expressions import Q
from tortoise.queryset import QuerySet
from tortoise.functions import Count, Sum

from app.models.strm import StrmTask, StrmFile, DownloadTask, DownloadLog, StrmLog
from app.models.system import User
from app.log.log import log


class OptimizedQueryController:
    """优化的查询控制器"""

    @staticmethod
    async def get_user_tasks_optimized(
        user: User,
        page: int = 1,
        page_size: int = 10,
        search: Optional[str] = None,
        status: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        获取用户任务列表（优化版本）
        使用select_related避免N+1查询
        """
        offset = (page - 1) * page_size
        
        # 构建基本查询，预加载关联数据
        query = StrmTask.filter(created_by=user).select_related(
            "server", "download_server", "created_by"
        )
        
        # 添加搜索条件
        if search:
            query = query.filter(name__icontains=search)
        
        # 添加状态过滤
        if status:
            query = query.filter(status=status)
        
        # 添加日期过滤
        if start_date:
            try:
                start_dt = datetime.strptime(start_date, "%Y-%m-%d")
                query = query.filter(create_time__gte=start_dt)
            except ValueError:
                log.warning(f"无效的开始日期格式: {start_date}")
        
        if end_date:
            try:
                end_dt = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
                query = query.filter(create_time__lt=end_dt)
            except ValueError:
                log.warning(f"无效的结束日期格式: {end_date}")
        
        # 执行查询
        tasks = await query.offset(offset).limit(page_size).order_by("-create_time")
        total = await query.count()
        
        # 转换为字典格式，避免额外查询
        task_list = []
        for task in tasks:
            task_dict = {
                "id": task.id,
                "name": task.name,
                "status": task.status,
                "total_files": task.total_files,
                "processed_files": task.processed_files,
                "success_files": task.success_files,
                "failed_files": task.failed_files,
                "progress": int((task.processed_files / task.total_files * 100)) if task.total_files > 0 else 0,
                "start_time": task.start_time.strftime("%Y-%m-%d %H:%M:%S") if task.start_time else None,
                "end_time": task.end_time.strftime("%Y-%m-%d %H:%M:%S") if task.end_time else None,
                "create_time": task.create_time.strftime("%Y-%m-%d %H:%M:%S"),
                "server_name": task.server.name if task.server else None,
                "download_server_name": task.download_server.name if task.download_server else None,
            }
            task_list.append(task_dict)
        
        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "tasks": task_list,
        }

    @staticmethod
    async def get_task_files_optimized(
        task_id: int, 
        page: int = 1, 
        page_size: int = 20,
        file_type: Optional[str] = None,
        success_only: Optional[bool] = None
    ) -> Dict[str, Any]:
        """
        优化的任务文件查询
        使用索引和字段选择优化
        """
        offset = (page - 1) * page_size
        
        # 构建查询
        query = StrmFile.filter(task_id=task_id)
        
        # 添加文件类型过滤
        if file_type:
            query = query.filter(file_type=file_type)
        
        # 添加成功状态过滤
        if success_only is not None:
            query = query.filter(is_success=success_only)
        
        # 只选择需要的字段，减少数据传输
        files = await query.only(
            "id", "source_path", "target_path", "file_type", 
            "file_size", "is_success", "error_message", "create_time"
        ).offset(offset).limit(page_size).order_by("create_time")
        
        total = await query.count()
        
        # 转换为字典格式
        file_list = []
        for file in files:
            file_dict = {
                "id": file.id,
                "source_path": file.source_path,
                "target_path": file.target_path,
                "file_type": file.file_type,
                "file_size": file.file_size,
                "is_success": file.is_success,
                "error_message": file.error_message,
                "create_time": file.create_time.strftime("%Y-%m-%d %H:%M:%S"),
            }
            file_list.append(file_dict)
        
        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "files": file_list,
        }

    @staticmethod
    async def get_task_logs_unified(
        task_id: int, 
        page: int = 1, 
        page_size: int = 50,
        level: Optional[str] = None,
        search: Optional[str] = None,
        log_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        优化的统一日志查询
        使用更高效的查询策略
        """
        offset = (page - 1) * page_size
        
        # 根据log_type决定查询策略
        if log_type == "download":
            # 只查询下载日志
            query = DownloadLog.filter(task_id=task_id)
            if level:
                query = query.filter(log_level=level.upper())
            if search:
                query = query.filter(log_message__icontains=search)
            
            logs = await query.only(
                "id", "file_path", "file_type", "is_success", 
                "log_level", "log_message", "create_time"
            ).offset(offset).limit(page_size).order_by("create_time")
            
            total = await query.count()
            
            # 转换格式
            log_list = []
            for log_item in logs:
                log_dict = {
                    "id": log_item.id,
                    "log_type": "download",
                    "file_path": log_item.file_path,
                    "file_type": log_item.file_type,
                    "is_success": log_item.is_success,
                    "log_level": log_item.log_level,
                    "log_message": log_item.log_message,
                    "create_time": log_item.create_time.strftime("%Y-%m-%d %H:%M:%S"),
                }
                log_list.append(log_dict)
                
        elif log_type == "strm":
            # 只查询STRM日志
            query = StrmLog.filter(task_id=task_id)
            if level:
                query = query.filter(log_level=level.upper())
            if search:
                query = query.filter(log_message__icontains=search)
            
            logs = await query.only(
                "id", "source_path", "target_path", "file_type", "is_success", 
                "log_level", "log_message", "create_time"
            ).offset(offset).limit(page_size).order_by("create_time")
            
            total = await query.count()
            
            # 转换格式
            log_list = []
            for log_item in logs:
                log_dict = {
                    "id": log_item.id,
                    "log_type": "strm",
                    "source_path": log_item.source_path,
                    "target_path": log_item.target_path,
                    "file_type": log_item.file_type,
                    "is_success": log_item.is_success,
                    "log_level": log_item.log_level,
                    "log_message": log_item.log_message,
                    "create_time": log_item.create_time.strftime("%Y-%m-%d %H:%M:%S"),
                }
                log_list.append(log_dict)
        else:
            # 查询所有日志（使用分别查询然后合并的策略，避免复杂的UNION）
            download_query = DownloadLog.filter(task_id=task_id)
            strm_query = StrmLog.filter(task_id=task_id)
            
            if level:
                download_query = download_query.filter(log_level=level.upper())
                strm_query = strm_query.filter(log_level=level.upper())
            if search:
                download_query = download_query.filter(log_message__icontains=search)
                strm_query = strm_query.filter(log_message__icontains=search)
            
            # 获取总数
            download_total = await download_query.count()
            strm_total = await strm_query.count()
            total = download_total + strm_total
            
            # 分别获取日志并合并
            download_logs = await download_query.only(
                "id", "file_path", "file_type", "is_success", 
                "log_level", "log_message", "create_time"
            ).order_by("create_time")
            
            strm_logs = await strm_query.only(
                "id", "source_path", "target_path", "file_type", "is_success", 
                "log_level", "log_message", "create_time"
            ).order_by("create_time")
            
            # 合并并排序
            all_logs = []
            
            for log_item in download_logs:
                log_dict = {
                    "id": f"download_{log_item.id}",
                    "log_type": "download",
                    "file_path": log_item.file_path,
                    "file_type": log_item.file_type,
                    "is_success": log_item.is_success,
                    "log_level": log_item.log_level,
                    "log_message": log_item.log_message,
                    "create_time": log_item.create_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "sort_time": log_item.create_time,
                }
                all_logs.append(log_dict)
            
            for log_item in strm_logs:
                log_dict = {
                    "id": f"strm_{log_item.id}",
                    "log_type": "strm",
                    "source_path": log_item.source_path,
                    "target_path": log_item.target_path,
                    "file_type": log_item.file_type,
                    "is_success": log_item.is_success,
                    "log_level": log_item.log_level,
                    "log_message": log_item.log_message,
                    "create_time": log_item.create_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "sort_time": log_item.create_time,
                }
                all_logs.append(log_dict)
            
            # 按时间排序并分页
            all_logs.sort(key=lambda x: x["sort_time"])
            log_list = all_logs[offset:offset + page_size]
            
            # 移除排序用的字段
            for log_dict in log_list:
                del log_dict["sort_time"]
        
        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "logs": log_list,
        }

    @staticmethod
    async def get_task_statistics(task_id: int) -> Dict[str, Any]:
        """
        获取任务统计信息（优化版本）
        使用聚合查询减少数据库访问
        """
        # 使用聚合查询获取文件统计
        file_stats = await StrmFile.filter(task_id=task_id).aggregate(
            total_files=Count("id"),
            success_files=Count("id", _filter=Q(is_success=True)),
            failed_files=Count("id", _filter=Q(is_success=False)),
            total_size=Sum("file_size")
        )
        
        # 获取日志统计
        download_log_count = await DownloadLog.filter(task_id=task_id).count()
        strm_log_count = await StrmLog.filter(task_id=task_id).count()
        
        return {
            "file_stats": file_stats,
            "log_stats": {
                "download_logs": download_log_count,
                "strm_logs": strm_log_count,
                "total_logs": download_log_count + strm_log_count,
            }
        }
