"""
文件内容优化控制器
专门优化数据库中存储文件内容的查询性能
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import hashlib
import gzip
import base64

from app.models.strm.upload import UploadRecord, UploadStatus
from app.models.system import User
from app.core.enhanced_cache_manager import EnhancedCacheManager, smart_cache
from app.log.log import log


class FileContentOptimizer:
    """文件内容优化器"""

    @staticmethod
    async def get_upload_list_optimized(
        user: User,
        page: int = 1,
        page_size: int = 10,
        status: Optional[str] = None,
        search: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取上传记录列表（优化版本）
        不包含文件内容，提升查询性能
        """
        offset = (page - 1) * page_size
        
        # 构建查询，排除文件内容字段
        query = UploadRecord.filter(uploader=user).only(
            "id", "filename", "filesize", "status", 
            "create_time", "parse_time", "uploader_id"
        )
        
        # 添加状态过滤
        if status:
            query = query.filter(status=status)
        
        # 添加文件名搜索
        if search:
            query = query.filter(filename__icontains=search)
        
        # 执行查询
        records = await query.offset(offset).limit(page_size).order_by("-create_time")
        total = await query.count()
        
        # 转换为字典格式
        record_list = []
        for record in records:
            record_dict = {
                "id": record.id,
                "filename": record.filename,
                "filesize": record.filesize,
                "status": record.status,
                "create_time": record.create_time.strftime("%Y-%m-%d %H:%M:%S"),
                "parse_time": record.parse_time.strftime("%Y-%m-%d %H:%M:%S") if record.parse_time else None,
                "uploader_id": record.uploader_id,
                # 添加文件大小的人类可读格式
                "filesize_human": FileContentOptimizer._format_file_size(record.filesize),
            }
            record_list.append(record_dict)
        
        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "records": record_list,
        }

    @staticmethod
    @smart_cache(cache_type="file_content", expire=1800)  # 缓存30分钟
    async def get_file_content_cached(record_id: int) -> Optional[bytes]:
        """
        获取文件内容（带缓存）
        """
        try:
            record = await UploadRecord.get_or_none(id=record_id)
            if not record:
                return None
            
            return record.file_content
            
        except Exception as e:
            log.error(f"获取文件内容失败: record_id={record_id}, error={str(e)}")
            return None

    @staticmethod
    async def get_file_content_info(record_id: int) -> Optional[Dict[str, Any]]:
        """
        获取文件内容信息（不包含实际内容）
        """
        try:
            record = await UploadRecord.get_or_none(id=record_id)
            if not record:
                return None
            
            # 计算文件内容哈希（用于验证）
            content_hash = None
            if record.file_content:
                content_hash = hashlib.md5(record.file_content).hexdigest()
            
            return {
                "id": record.id,
                "filename": record.filename,
                "filesize": record.filesize,
                "filesize_human": FileContentOptimizer._format_file_size(record.filesize),
                "status": record.status,
                "content_hash": content_hash,
                "has_content": record.file_content is not None,
                "create_time": record.create_time.strftime("%Y-%m-%d %H:%M:%S"),
                "parse_time": record.parse_time.strftime("%Y-%m-%d %H:%M:%S") if record.parse_time else None,
            }
            
        except Exception as e:
            log.error(f"获取文件信息失败: record_id={record_id}, error={str(e)}")
            return None

    @staticmethod
    async def get_file_content_preview(record_id: int, max_lines: int = 50) -> Optional[Dict[str, Any]]:
        """
        获取文件内容预览（前几行）
        """
        try:
            content = await FileContentOptimizer.get_file_content_cached(record_id)
            if not content:
                return None
            
            # 解码文件内容
            try:
                text_content = content.decode('utf-8')
            except UnicodeDecodeError:
                try:
                    text_content = content.decode('gbk')
                except UnicodeDecodeError:
                    return {"error": "无法解码文件内容，可能不是文本文件"}
            
            # 获取前几行
            lines = text_content.split('\n')
            preview_lines = lines[:max_lines]
            
            return {
                "preview": '\n'.join(preview_lines),
                "total_lines": len(lines),
                "is_truncated": len(lines) > max_lines,
                "encoding": "utf-8",
            }
            
        except Exception as e:
            log.error(f"获取文件预览失败: record_id={record_id}, error={str(e)}")
            return {"error": f"获取预览失败: {str(e)}"}

    @staticmethod
    async def compress_file_content(record_id: int) -> bool:
        """
        压缩文件内容（可选优化）
        """
        try:
            record = await UploadRecord.get_or_none(id=record_id)
            if not record or not record.file_content:
                return False
            
            # 检查是否已经压缩
            if record.filename.endswith('.gz'):
                return True
            
            # 压缩内容
            compressed_content = gzip.compress(record.file_content)
            
            # 如果压缩后更小，则更新
            if len(compressed_content) < len(record.file_content):
                record.file_content = compressed_content
                record.filename = record.filename + '.gz'
                await record.save()
                
                log.info(f"文件 {record_id} 压缩成功: {len(record.file_content)} -> {len(compressed_content)} bytes")
                
                # 清除缓存
                cache_key = f"file_content:{record_id}"
                await EnhancedCacheManager.invalidate_cache(pattern=cache_key)
                
                return True
            
            return False
            
        except Exception as e:
            log.error(f"压缩文件内容失败: record_id={record_id}, error={str(e)}")
            return False

    @staticmethod
    async def get_upload_statistics(user: User) -> Dict[str, Any]:
        """
        获取用户上传统计信息
        """
        try:
            # 使用聚合查询获取统计信息
            from tortoise.functions import Count, Sum
            
            stats = await UploadRecord.filter(uploader=user).aggregate(
                total_files=Count("id"),
                total_size=Sum("filesize"),
                uploaded_count=Count("id", _filter={"status": UploadStatus.UPLOADED}),
                parsing_count=Count("id", _filter={"status": UploadStatus.PARSING}),
                parsed_count=Count("id", _filter={"status": UploadStatus.PARSED}),
                failed_count=Count("id", _filter={"status": UploadStatus.FAILED}),
            )
            
            # 计算平均文件大小
            avg_size = 0
            if stats["total_files"] > 0 and stats["total_size"]:
                avg_size = stats["total_size"] / stats["total_files"]
            
            return {
                "total_files": stats["total_files"] or 0,
                "total_size": stats["total_size"] or 0,
                "total_size_human": FileContentOptimizer._format_file_size(stats["total_size"] or 0),
                "average_size": avg_size,
                "average_size_human": FileContentOptimizer._format_file_size(avg_size),
                "status_breakdown": {
                    "uploaded": stats["uploaded_count"] or 0,
                    "parsing": stats["parsing_count"] or 0,
                    "parsed": stats["parsed_count"] or 0,
                    "failed": stats["failed_count"] or 0,
                }
            }
            
        except Exception as e:
            log.error(f"获取上传统计失败: user_id={user.id}, error={str(e)}")
            return {
                "total_files": 0,
                "total_size": 0,
                "total_size_human": "0 B",
                "average_size": 0,
                "average_size_human": "0 B",
                "status_breakdown": {
                    "uploaded": 0,
                    "parsing": 0,
                    "parsed": 0,
                    "failed": 0,
                }
            }

    @staticmethod
    async def cleanup_old_files(days: int = 30) -> int:
        """
        清理旧的上传文件（可选）
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # 查找旧文件
            old_records = await UploadRecord.filter(
                create_time__lt=cutoff_date,
                status__in=[UploadStatus.FAILED, UploadStatus.UPLOADED]  # 只清理失败或未处理的
            ).all()
            
            cleaned_count = 0
            for record in old_records:
                # 清空文件内容但保留记录
                if record.file_content:
                    record.file_content = None
                    await record.save()
                    cleaned_count += 1
            
            log.info(f"清理了 {cleaned_count} 个旧文件的内容")
            return cleaned_count
            
        except Exception as e:
            log.error(f"清理旧文件失败: error={str(e)}")
            return 0

    @staticmethod
    def _format_file_size(size_bytes: int) -> str:
        """
        格式化文件大小为人类可读格式
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"

    @staticmethod
    async def validate_file_integrity(record_id: int) -> Dict[str, Any]:
        """
        验证文件完整性
        """
        try:
            record = await UploadRecord.get_or_none(id=record_id)
            if not record:
                return {"valid": False, "error": "记录不存在"}
            
            if not record.file_content:
                return {"valid": False, "error": "文件内容为空"}
            
            # 验证文件大小
            actual_size = len(record.file_content)
            if actual_size != record.filesize:
                return {
                    "valid": False,
                    "error": f"文件大小不匹配: 记录={record.filesize}, 实际={actual_size}"
                }
            
            # 计算文件哈希
            content_hash = hashlib.md5(record.file_content).hexdigest()
            
            return {
                "valid": True,
                "filesize": actual_size,
                "content_hash": content_hash,
                "filename": record.filename,
            }
            
        except Exception as e:
            log.error(f"验证文件完整性失败: record_id={record_id}, error={str(e)}")
            return {"valid": False, "error": f"验证失败: {str(e)}"}


# 缓存预热函数
async def warmup_file_content_cache(user_id: int, limit: int = 10):
    """
    预热文件内容缓存
    """
    try:
        # 获取用户最近的文件
        recent_records = await UploadRecord.filter(
            uploader_id=user_id,
            status=UploadStatus.PARSED
        ).only("id").order_by("-create_time").limit(limit)
        
        # 预加载到缓存
        for record in recent_records:
            await FileContentOptimizer.get_file_content_cached(record.id)
        
        log.info(f"预热了 {len(recent_records)} 个文件的缓存")
        
    except Exception as e:
        log.error(f"缓存预热失败: user_id={user_id}, error={str(e)}")


__all__ = [
    "FileContentOptimizer",
    "warmup_file_content_cache"
]
