"""
增强的缓存管理器
提供统一的缓存操作接口，支持Redis缓存
"""

from typing import Any, Callable, Optional
import json
import hashlib
from app.log.log import log


class CacheManager:
    """增强的缓存管理器"""
    
    @staticmethod
    def get_cache_key(*args) -> str:
        """
        生成缓存键
        
        Args:
            *args: 用于生成缓存键的参数
            
        Returns:
            str: 生成的缓存键
        """
        # 将所有参数转换为字符串并连接
        key_parts = []
        for arg in args:
            if isinstance(arg, (dict, list)):
                # 对复杂对象进行JSON序列化
                key_parts.append(json.dumps(arg, sort_keys=True, ensure_ascii=False))
            else:
                key_parts.append(str(arg))
        
        # 生成缓存键
        cache_key = ":".join(key_parts)
        
        # 如果键太长，使用哈希值
        if len(cache_key) > 200:
            cache_key = hashlib.md5(cache_key.encode('utf-8')).hexdigest()
        
        return f"streamforge:{cache_key}"
    
    @staticmethod
    async def get_or_set_cache(
        key: str, 
        fetch_func: Callable,
        expire: int = 300,
        cache_null: bool = False
    ) -> Any:
        """
        获取或设置缓存
        
        Args:
            key: 缓存键
            fetch_func: 获取数据的函数
            expire: 过期时间（秒）
            cache_null: 是否缓存空值
            
        Returns:
            Any: 缓存的数据或从fetch_func获取的数据
        """
        try:
            from fastapi_cache import FastAPICache
            
            # 尝试从缓存获取数据
            cached_data = await FastAPICache.get_backend().get(key)
            if cached_data is not None:
                log.debug(f"缓存命中: {key}")
                return cached_data
            
            # 缓存未命中，执行获取函数
            log.debug(f"缓存未命中，执行查询: {key}")
            data = await fetch_func()
            
            # 设置缓存
            if data is not None or cache_null:
                await FastAPICache.get_backend().set(key, data, expire=expire)
                log.debug(f"数据已缓存: {key}, 过期时间: {expire}秒")
            
            return data
            
        except Exception as e:
            # 缓存操作失败时，直接返回数据，不影响业务逻辑
            log.warning(f"缓存操作失败: {str(e)}, 直接返回数据")
            return await fetch_func()
    
    @staticmethod
    async def invalidate_cache(key: str) -> bool:
        """
        清除指定缓存
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否成功清除
        """
        try:
            from fastapi_cache import FastAPICache
            await FastAPICache.get_backend().delete(key)
            log.debug(f"缓存已清除: {key}")
            return True
        except Exception as e:
            log.warning(f"清除缓存失败: {str(e)}")
            return False
    
    @staticmethod
    async def invalidate_pattern(pattern: str) -> int:
        """
        按模式清除缓存
        
        Args:
            pattern: 缓存键模式（支持通配符）
            
        Returns:
            int: 清除的缓存数量
        """
        try:
            from fastapi_cache import FastAPICache
            backend = FastAPICache.get_backend()
            
            # 获取Redis连接
            if hasattr(backend, 'redis'):
                redis = backend.redis
                # 查找匹配的键
                keys = await redis.keys(f"fastapi-cache:{pattern}")
                if keys:
                    # 批量删除
                    deleted_count = await redis.delete(*keys)
                    log.debug(f"按模式清除缓存: {pattern}, 清除数量: {deleted_count}")
                    return deleted_count
            
            return 0
        except Exception as e:
            log.warning(f"按模式清除缓存失败: {str(e)}")
            return 0
    
    @staticmethod
    async def get_cache_info(key: str) -> Optional[dict]:
        """
        获取缓存信息
        
        Args:
            key: 缓存键
            
        Returns:
            dict: 缓存信息（包括TTL等）
        """
        try:
            from fastapi_cache import FastAPICache
            backend = FastAPICache.get_backend()
            
            if hasattr(backend, 'redis'):
                redis = backend.redis
                full_key = f"fastapi-cache:{key}"
                
                # 检查键是否存在
                exists = await redis.exists(full_key)
                if not exists:
                    return None
                
                # 获取TTL
                ttl = await redis.ttl(full_key)
                
                # 获取数据类型
                data_type = await redis.type(full_key)
                
                return {
                    "key": key,
                    "exists": bool(exists),
                    "ttl": ttl,
                    "type": data_type.decode() if isinstance(data_type, bytes) else data_type
                }
            
            return None
        except Exception as e:
            log.warning(f"获取缓存信息失败: {str(e)}")
            return None


class QueryCacheManager:
    """查询缓存管理器，专门用于数据库查询缓存"""
    
    @staticmethod
    def get_user_list_cache_key(
        user_id: int, 
        page: int, 
        page_size: int, 
        search: str = "", 
        filters: dict = None
    ) -> str:
        """生成用户列表缓存键"""
        filters = filters or {}
        return CacheManager.get_cache_key(
            "user_list", user_id, page, page_size, search, filters
        )
    
    @staticmethod
    def get_api_list_cache_key(
        user_id: int, 
        page: int, 
        page_size: int, 
        search: str = "", 
        filters: dict = None
    ) -> str:
        """生成API列表缓存键"""
        filters = filters or {}
        return CacheManager.get_cache_key(
            "api_list", user_id, page, page_size, search, filters
        )
    
    @staticmethod
    def get_task_list_cache_key(
        user_id: int, 
        page: int, 
        page_size: int, 
        search: str = "", 
        status: str = "",
        start_date: str = "",
        end_date: str = ""
    ) -> str:
        """生成任务列表缓存键"""
        return CacheManager.get_cache_key(
            "task_list", user_id, page, page_size, search, status, start_date, end_date
        )
    
    @staticmethod
    async def invalidate_user_caches(user_id: int):
        """清除用户相关的所有缓存"""
        patterns = [
            f"streamforge:user_list:{user_id}:*",
            f"streamforge:api_list:{user_id}:*",
            f"streamforge:task_list:{user_id}:*",
        ]
        
        total_cleared = 0
        for pattern in patterns:
            cleared = await CacheManager.invalidate_pattern(pattern)
            total_cleared += cleared
        
        log.info(f"用户 {user_id} 相关缓存已清除，共 {total_cleared} 个")
        return total_cleared


# 缓存装饰器
def cache_result(expire: int = 300, key_prefix: str = ""):
    """
    缓存结果装饰器
    
    Args:
        expire: 过期时间（秒）
        key_prefix: 缓存键前缀
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = CacheManager.get_cache_key(
                key_prefix, func.__name__, args, kwargs
            )
            
            # 使用缓存管理器
            return await CacheManager.get_or_set_cache(
                cache_key, 
                lambda: func(*args, **kwargs),
                expire=expire
            )
        
        return wrapper
    return decorator


__all__ = [
    "CacheManager",
    "QueryCacheManager", 
    "cache_result"
]
