"""
数据聚合器模块
用于聚合监控数据，支持小时级和日级数据聚合
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional
from tortoise.functions import Avg, <PERSON>, <PERSON>, Count, Sum
from tortoise.expressions import Q

from app.log import log


class DataAggregator:
    """监控数据聚合器（基于现有APILog和StrmTask）"""

    @staticmethod
    async def _get_models():
        """延迟导入模型以避免循环依赖"""
        from app.models.system import APILog
        from app.models.strm import StrmTask, TaskStatus
        from app.models.monitor import MonitorHourlyStats, MonitorDailyStats
        return APILog, StrmTask, TaskStatus, MonitorHourlyStats, MonitorDailyStats

    @staticmethod
    async def aggregate_hourly_data(target_date: datetime = None) -> Dict:
        """聚合小时级数据"""
        if not target_date:
            target_date = datetime.now() - timedelta(hours=1)

        hour_start = target_date.replace(minute=0, second=0, microsecond=0)
        hour_end = hour_start + timedelta(hours=1)

        try:
            # 获取模型
            APILog, StrmTask, TaskStatus, MonitorHourlyStats, MonitorDailyStats = await DataAggregator._get_models()

            # 检查是否已经聚合过这个小时的数据
            existing = await MonitorHourlyStats.filter(date_hour=hour_start).first()
            if existing:
                log.info(f"小时级数据 {hour_start} 已存在，跳过聚合")
                return {"status": "skipped", "reason": "data_exists", "hour": hour_start.isoformat()}

            # 聚合API日志数据
            api_logs = await APILog.filter(
                create_time__gte=hour_start,
                create_time__lt=hour_end
            ).all()

            total_requests = len(api_logs)
            process_times = [log.process_time for log in api_logs if log.process_time is not None]
            
            avg_response_time = sum(process_times) / len(process_times) if process_times else 0
            max_response_time = max(process_times) if process_times else 0
            min_response_time = min(process_times) if process_times else 0
            error_count = len([log for log in api_logs if log.response_code and int(log.response_code) >= 400])

            # 聚合STRM任务数据
            strm_tasks_created = await StrmTask.filter(
                create_time__gte=hour_start,
                create_time__lt=hour_end
            ).count()

            strm_tasks_completed = await StrmTask.filter(
                end_time__gte=hour_start,
                end_time__lt=hour_end,
                status=TaskStatus.COMPLETED
            ).count()

            strm_tasks_failed = await StrmTask.filter(
                end_time__gte=hour_start,
                end_time__lt=hour_end,
                status=TaskStatus.FAILED
            ).count()

            # 存储聚合数据
            stats = await MonitorHourlyStats.create(
                date_hour=hour_start,
                total_requests=total_requests,
                avg_response_time=avg_response_time,
                max_response_time=max_response_time,
                min_response_time=min_response_time,
                error_count=error_count,
                strm_tasks_created=strm_tasks_created,
                strm_tasks_completed=strm_tasks_completed,
                strm_tasks_failed=strm_tasks_failed
            )

            log.info(f"✅ 完成 {hour_start} 的小时级数据聚合 (请求: {total_requests}, 任务: {strm_tasks_created})")
            
            return {
                "status": "success",
                "hour": hour_start.isoformat(),
                "stats": {
                    "total_requests": total_requests,
                    "avg_response_time": avg_response_time,
                    "strm_tasks_created": strm_tasks_created,
                    "strm_tasks_completed": strm_tasks_completed,
                    "strm_tasks_failed": strm_tasks_failed
                }
            }

        except Exception as e:
            log.error(f"❌ 聚合小时级数据失败 {hour_start}: {str(e)}")
            return {"status": "error", "hour": hour_start.isoformat(), "error": str(e)}

    @staticmethod
    async def aggregate_daily_data(target_date: datetime = None) -> Dict:
        """聚合日级数据"""
        if not target_date:
            target_date = datetime.now() - timedelta(days=1)

        start_time = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = start_time + timedelta(days=1)

        try:
            # 获取模型
            APILog, StrmTask, TaskStatus, MonitorHourlyStats, MonitorDailyStats = await DataAggregator._get_models()

            # 检查是否已经聚合过这一天的数据
            existing = await MonitorDailyStats.filter(date=start_time.date()).first()
            if existing:
                log.info(f"日级数据 {start_time.date()} 已存在，跳过聚合")
                return {"status": "skipped", "reason": "data_exists", "date": start_time.date().isoformat()}

            # 从小时级数据聚合到日级数据
            hourly_stats = await MonitorHourlyStats.filter(
                date_hour__gte=start_time,
                date_hour__lt=end_time
            ).all()

            if not hourly_stats:
                log.warning(f"没有找到 {start_time.date()} 的小时级数据，跳过日级聚合")
                return {"status": "skipped", "reason": "no_hourly_data", "date": start_time.date().isoformat()}

            # 计算日级统计
            total_requests = sum(stat.total_requests for stat in hourly_stats)
            avg_response_time = sum(stat.avg_response_time for stat in hourly_stats) / len(hourly_stats)
            max_response_time = max(stat.max_response_time for stat in hourly_stats)
            min_response_time = min(stat.min_response_time for stat in hourly_stats if stat.min_response_time > 0)
            total_errors = sum(stat.error_count for stat in hourly_stats)
            strm_tasks_total = sum(stat.strm_tasks_created for stat in hourly_stats)
            strm_tasks_completed = sum(stat.strm_tasks_completed for stat in hourly_stats)
            strm_tasks_failed = sum(stat.strm_tasks_failed for stat in hourly_stats)
            success_rate = strm_tasks_completed / strm_tasks_total if strm_tasks_total > 0 else 0

            # 存储日级聚合数据
            daily_stats = await MonitorDailyStats.create(
                date=start_time.date(),
                total_requests=total_requests,
                avg_response_time=avg_response_time,
                max_response_time=max_response_time,
                min_response_time=min_response_time if min_response_time != float('inf') else 0,
                total_errors=total_errors,
                strm_tasks_total=strm_tasks_total,
                strm_tasks_completed=strm_tasks_completed,
                strm_tasks_failed=strm_tasks_failed,
                strm_tasks_success_rate=success_rate
            )

            log.info(f"✅ 完成 {start_time.date()} 的日级数据聚合 (请求: {total_requests}, 任务: {strm_tasks_total})")
            
            return {
                "status": "success",
                "date": start_time.date().isoformat(),
                "stats": {
                    "total_requests": total_requests,
                    "avg_response_time": avg_response_time,
                    "strm_tasks_total": strm_tasks_total,
                    "strm_tasks_success_rate": success_rate
                }
            }

        except Exception as e:
            log.error(f"❌ 聚合日级数据失败 {start_time.date()}: {str(e)}")
            return {"status": "error", "date": start_time.date().isoformat(), "error": str(e)}

    @staticmethod
    async def cleanup_old_data() -> Dict:
        """清理过期的原始数据"""
        try:
            # 获取模型
            APILog, StrmTask, TaskStatus, MonitorHourlyStats, MonitorDailyStats = await DataAggregator._get_models()

            # 删除超过7天的原始API日志（保留聚合数据）
            cutoff_date = datetime.now() - timedelta(days=7)
            deleted_api_logs = await APILog.filter(create_time__lt=cutoff_date).delete()

            # 删除超过90天的小时级聚合数据
            cutoff_date_hourly = datetime.now() - timedelta(days=90)
            deleted_hourly = await MonitorHourlyStats.filter(date_hour__lt=cutoff_date_hourly).delete()

            # 删除超过365天的日级聚合数据
            cutoff_date_daily = datetime.now() - timedelta(days=365)
            deleted_daily = await MonitorDailyStats.filter(date__lt=cutoff_date_daily.date()).delete()

            log.info(f"🧹 数据清理完成: API日志 {deleted_api_logs} 条, 小时级数据 {deleted_hourly} 条, 日级数据 {deleted_daily} 条")
            
            return {
                "status": "success",
                "deleted": {
                    "api_logs": deleted_api_logs,
                    "hourly_stats": deleted_hourly,
                    "daily_stats": deleted_daily
                }
            }

        except Exception as e:
            log.error(f"❌ 清理过期数据失败: {str(e)}")
            return {"status": "error", "error": str(e)}

    @staticmethod
    async def get_aggregation_summary() -> Dict:
        """获取聚合数据概览"""
        try:
            # 获取模型
            APILog, StrmTask, TaskStatus, MonitorHourlyStats, MonitorDailyStats = await DataAggregator._get_models()

            hourly_count = await MonitorHourlyStats.all().count()
            daily_count = await MonitorDailyStats.all().count()

            # 获取最新的聚合数据时间
            latest_hourly = await MonitorHourlyStats.all().order_by('-date_hour').first()
            latest_daily = await MonitorDailyStats.all().order_by('-date').first()
            
            return {
                "hourly_stats_count": hourly_count,
                "daily_stats_count": daily_count,
                "latest_hourly": latest_hourly.date_hour.isoformat() if latest_hourly else None,
                "latest_daily": latest_daily.date.isoformat() if latest_daily else None
            }
        except Exception as e:
            log.error(f"❌ 获取聚合数据概览失败: {str(e)}")
            return {"error": str(e)}


__all__ = [
    "DataAggregator"
]
