"""
数据库查询优化工具类
提供常用的查询优化方法，避免N+1查询问题，提升查询性能
"""

from typing import List, Dict, Any, Optional, Union
from tortoise.models import Model
from tortoise.queryset import QuerySet
from tortoise.expressions import Q
from tortoise.transactions import in_transaction
from app.models.strm import StrmTask, StrmFile, DownloadTask, DownloadLog, StrmLog, MediaServer
from app.models.system import User, Role


class QueryOptimizer:
    """查询优化器"""

    @staticmethod
    async def get_tasks_with_relations(
        user: User,
        page: int = 1,
        page_size: int = 10,
        search: Optional[str] = None,
        status: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        优化的任务列表查询，使用预加载避免N+1查询
        """
        from datetime import datetime
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 构建基本查询，预加载关联数据
        query = StrmTask.filter(created_by=user).select_related(
            "server", "download_server", "created_by"
        )
        
        # 添加搜索条件
        if search:
            query = query.filter(name__icontains=search)
        
        # 添加状态过滤
        if status:
            status_mapping = {
                "SUCCESS": "completed",
                "CANCELED": "canceled",
                "FAILED": "failed",
                "PENDING": "pending",
                "RUNNING": "running",
            }
            backend_status = status_mapping.get(status, status.lower())
            query = query.filter(status=backend_status)
        
        # 添加日期范围过滤
        if start_date:
            start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
            query = query.filter(create_time__gte=start_datetime)
        
        if end_date:
            end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
            end_datetime = end_datetime.replace(hour=23, minute=59, second=59, microsecond=999999)
            query = query.filter(create_time__lte=end_datetime)
        
        # 执行查询
        tasks = await query.offset(offset).limit(page_size).order_by("-create_time")
        total = await query.count()
        
        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "tasks": tasks,
        }

    @staticmethod
    async def get_task_files_optimized(task_id: int, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        优化的任务文件查询，使用索引和字段选择优化
        """
        offset = (page - 1) * page_size
        
        # 只选择需要的字段，减少数据传输
        files = await StrmFile.filter(task_id=task_id).only(
            "id", "source_path", "target_path", "file_type", 
            "file_size", "is_success", "error_message", "create_time"
        ).offset(offset).limit(page_size).order_by("create_time")
        
        total = await StrmFile.filter(task_id=task_id).count()
        
        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "files": files,
        }

    @staticmethod
    async def get_unified_task_logs(
        task_id: int, 
        page: int = 1, 
        page_size: int = 50,
        level: Optional[str] = None,
        search: Optional[str] = None,
        log_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        优化的统一日志查询，使用UNION查询替代分别查询
        """
        from tortoise import connections
        
        # 构建查询条件
        conditions = ["task_id = $1"]
        params = [task_id]
        param_index = 2
        
        if level:
            conditions.append(f"log_level = ${param_index}")
            params.append(level.upper())
            param_index += 1
        
        if search:
            conditions.append(f"log_message LIKE ${param_index}")
            params.append(f"%{search}%")
            param_index += 1
        
        where_clause = " AND ".join(conditions)
        
        # 构建UNION查询
        if not log_type or log_type == "all":
            # 查询所有类型的日志
            sql = f"""
            SELECT 
                'download' as log_type,
                create_time,
                log_level,
                log_message,
                source_path,
                target_path,
                is_success,
                error_message,
                download_time as processing_time
            FROM strm_download_logs 
            WHERE {where_clause}
            
            UNION ALL
            
            SELECT 
                'strm' as log_type,
                create_time,
                log_level,
                log_message,
                source_path,
                target_path,
                is_success,
                error_message,
                generation_time as processing_time
            FROM strm_logs 
            WHERE {where_clause}
            
            ORDER BY create_time
            LIMIT {page_size} OFFSET {(page - 1) * page_size}
            """
        elif log_type == "download":
            sql = f"""
            SELECT 
                'download' as log_type,
                create_time,
                log_level,
                log_message,
                source_path,
                target_path,
                is_success,
                error_message,
                download_time as processing_time
            FROM strm_download_logs 
            WHERE {where_clause}
            ORDER BY create_time
            LIMIT {page_size} OFFSET {(page - 1) * page_size}
            """
        else:  # strm
            sql = f"""
            SELECT 
                'strm' as log_type,
                create_time,
                log_level,
                log_message,
                source_path,
                target_path,
                is_success,
                error_message,
                generation_time as processing_time
            FROM strm_logs 
            WHERE {where_clause}
            ORDER BY create_time
            LIMIT {page_size} OFFSET {(page - 1) * page_size}
            """
        
        # 执行查询
        conn = connections.get("conn_system")
        logs = await conn.execute_query(sql, params)
        
        # 计算总数
        count_sql = f"""
        SELECT COUNT(*) as total FROM (
            SELECT 1 FROM strm_download_logs WHERE {where_clause}
            UNION ALL
            SELECT 1 FROM strm_logs WHERE {where_clause}
        )
        """
        total_result = await conn.execute_query(count_sql, params)
        total = total_result[1][0]["total"] if total_result[1] else 0
        
        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "logs": logs[1] if logs[1] else [],
        }

    @staticmethod
    async def bulk_create_strm_files(files_data: List[Dict[str, Any]]) -> List[StrmFile]:
        """
        批量创建STRM文件记录，提升插入性能
        """
        async with in_transaction():
            files = []
            for data in files_data:
                file_obj = StrmFile(**data)
                files.append(file_obj)
            
            # 使用bulk_create批量插入
            created_files = await StrmFile.bulk_create(files)
            return created_files

    @staticmethod
    async def bulk_update_task_status(task_ids: List[int], status: str) -> int:
        """
        批量更新任务状态
        """
        async with in_transaction():
            updated_count = await StrmTask.filter(id__in=task_ids).update(status=status)
            return updated_count

    @staticmethod
    async def get_task_statistics(user: User) -> Dict[str, Any]:
        """
        获取任务统计信息，使用聚合查询优化性能
        """
        from tortoise.functions import Count
        
        # 使用聚合查询获取统计信息
        stats = await StrmTask.filter(created_by=user).annotate(
            total_count=Count("id")
        ).group_by("status").values("status", "total_count")
        
        # 转换为字典格式
        status_stats = {item["status"]: item["total_count"] for item in stats}
        
        # 获取文件统计
        file_stats = await StrmFile.filter(task__created_by=user).annotate(
            total_count=Count("id")
        ).group_by("file_type", "is_success").values("file_type", "is_success", "total_count")
        
        return {
            "task_stats": status_stats,
            "file_stats": file_stats,
        }


class CacheManager:
    """缓存管理器"""
    
    @staticmethod
    def get_cache_key(prefix: str, *args) -> str:
        """生成缓存键"""
        return f"{prefix}:{':'.join(map(str, args))}"
    
    @staticmethod
    async def get_or_set_cache(cache_key: str, fetch_func, expire: int = 300):
        """获取或设置缓存"""
        from fastapi_cache import FastAPICache
        
        # 尝试从缓存获取
        cached_data = await FastAPICache.get(cache_key)
        if cached_data is not None:
            return cached_data
        
        # 缓存未命中，执行查询
        data = await fetch_func()
        
        # 设置缓存
        await FastAPICache.set(cache_key, data, expire=expire)
        return data


__all__ = ["QueryOptimizer", "CacheManager"]
