import asyncio
from typing import Optional
from loguru import logger

from app.core.ctx import CTX_USER_ID, CTX_X_REQUEST_ID
from app.models.system import Log, LogType, LogDetailType


class AsyncLoggerManager:
    """异步日志记录管理器，用于优化日志记录性能"""
    
    def __init__(self):
        self._log_queue = asyncio.Queue()
        self._worker_task = None
        self._is_running = False
    
    async def start(self):
        """启动异步日志记录工作器"""
        if not self._is_running:
            self._is_running = True
            self._worker_task = asyncio.create_task(self._log_worker())
            logger.info("异步日志记录器已启动")
    
    async def stop(self):
        """停止异步日志记录工作器"""
        if self._is_running:
            self._is_running = False
            if self._worker_task:
                self._worker_task.cancel()
                try:
                    await self._worker_task
                except asyncio.CancelledError:
                    pass
            logger.info("异步日志记录器已停止")
    
    async def _log_worker(self):
        """日志记录工作器"""
        while self._is_running:
            try:
                # 等待日志记录任务
                log_data = await asyncio.wait_for(self._log_queue.get(), timeout=1.0)
                
                # 批量处理日志记录
                await self._process_log_batch([log_data])
                
                # 标记任务完成
                self._log_queue.task_done()
                
            except asyncio.TimeoutError:
                # 超时继续循环
                continue
            except Exception as e:
                logger.error(f"异步日志记录出错: {e}")
    
    async def _process_log_batch(self, log_batch: list):
        """批量处理日志记录"""
        try:
            for log_data in log_batch:
                await Log.create(**log_data)
        except Exception as e:
            logger.error(f"批量日志记录失败: {e}")
    
    async def add_log(
        self, 
        log_type: LogType, 
        log_detail_type: LogDetailType, 
        by_user_id: Optional[int] = None
    ):
        """添加日志记录到队列"""
        try:
            if by_user_id == 0:
                by_user_id = CTX_USER_ID.get() or None
            
            log_data = {
                "log_type": log_type,
                "log_detail_type": log_detail_type,
                "by_user_id": by_user_id,
                "x_request_id": CTX_X_REQUEST_ID.get()
            }
            
            # 如果队列未满，添加到队列；否则直接记录
            if self._log_queue.qsize() < 100:  # 队列大小限制
                await self._log_queue.put(log_data)
            else:
                # 队列满时直接同步记录，避免阻塞
                await Log.create(**log_data)
                
        except Exception as e:
            logger.error(f"添加异步日志失败: {e}")
            # 失败时回退到同步记录
            try:
                await Log.create(
                    log_type=log_type,
                    log_detail_type=log_detail_type,
                    by_user_id=by_user_id,
                    x_request_id=CTX_X_REQUEST_ID.get()
                )
            except Exception as sync_e:
                logger.error(f"同步日志记录也失败: {sync_e}")


# 全局异步日志管理器实例
_async_logger_manager = None


def get_async_logger_manager() -> AsyncLoggerManager:
    """获取全局异步日志管理器实例"""
    global _async_logger_manager
    if _async_logger_manager is None:
        _async_logger_manager = AsyncLoggerManager()
    return _async_logger_manager


async def insert_log_async(
    log_type: LogType, 
    log_detail_type: LogDetailType, 
    by_user_id: Optional[int] = None
):
    """
    异步插入日志，不阻塞主请求
    :param log_type: 日志类型
    :param log_detail_type: 日志详情类型
    :param by_user_id: 用户ID，0为从上下文获取当前用户id
    """
    manager = get_async_logger_manager()
    await manager.add_log(log_type, log_detail_type, by_user_id)


# 为了向后兼容，保留原有的同步接口
async def insert_log(
    log_type: LogType, 
    log_detail_type: LogDetailType, 
    by_user_id: Optional[int] = None
):
    """
    插入日志（向后兼容接口）
    :param log_type: 日志类型
    :param log_detail_type: 日志详情类型
    :param by_user_id: 用户ID，0为从上下文获取当前用户id
    """
    # 使用异步日志记录
    await insert_log_async(log_type, log_detail_type, by_user_id)
