"""
监控数据缓存管理器

提供监控数据的缓存管理功能，包括防击穿机制、TTL管理等。
避免频繁查询数据库，提升监控接口的响应性能。
"""

from functools import lru_cache
from datetime import datetime, timedelta
import asyncio
from typing import Dict, Any, Callable, Optional
from app.log import log


class MonitorCache:
    """监控数据缓存管理器"""
    
    _cache: Dict[str, Any] = {}
    _cache_ttl: Dict[str, datetime] = {}
    _locks: Dict[str, asyncio.Lock] = {}
    
    @classmethod
    async def get_or_set(cls, key: str, factory_func: Callable, ttl_seconds: int = 300) -> Any:
        """
        获取缓存数据，如果不存在则调用工厂函数生成
        
        Args:
            key: 缓存键
            factory_func: 数据生成函数
            ttl_seconds: 缓存过期时间（秒）
            
        Returns:
            缓存的数据
        """
        # 检查缓存是否有效
        if key in cls._cache and datetime.now() < cls._cache_ttl[key]:
            return cls._cache[key]

        # 防止缓存击穿，使用锁确保同一时间只有一个请求去获取数据
        if key not in cls._locks:
            cls._locks[key] = asyncio.Lock()

        async with cls._locks[key]:
            # 双重检查，可能在等待锁的过程中其他请求已经更新了缓存
            if key in cls._cache and datetime.now() < cls._cache_ttl[key]:
                return cls._cache[key]

            try:
                # 生成新数据
                if asyncio.iscoroutinefunction(factory_func):
                    data = await factory_func()
                else:
                    data = factory_func()

                # 设置缓存
                cls._cache[key] = data
                cls._cache_ttl[key] = datetime.now() + timedelta(seconds=ttl_seconds)

                log.debug(f"缓存已更新: {key}, TTL: {ttl_seconds}秒")
                return data
                
            except Exception as e:
                log.error(f"生成缓存数据失败 {key}: {str(e)}")
                # 如果生成数据失败，返回旧缓存（如果存在）
                if key in cls._cache:
                    log.warning(f"使用过期缓存数据: {key}")
                    return cls._cache[key]
                raise

    @classmethod
    def invalidate(cls, key: str) -> bool:
        """
        使缓存失效
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功删除
        """
        removed = False
        if key in cls._cache:
            cls._cache.pop(key, None)
            removed = True
        if key in cls._cache_ttl:
            cls._cache_ttl.pop(key, None)
        
        if removed:
            log.debug(f"缓存已失效: {key}")
        
        return removed

    @classmethod
    def invalidate_pattern(cls, pattern: str) -> int:
        """
        根据模式使缓存失效
        
        Args:
            pattern: 匹配模式（简单的字符串包含匹配）
            
        Returns:
            删除的缓存数量
        """
        keys_to_remove = [key for key in cls._cache.keys() if pattern in key]
        count = 0
        
        for key in keys_to_remove:
            if cls.invalidate(key):
                count += 1
        
        if count > 0:
            log.debug(f"批量失效缓存: {count} 个，模式: {pattern}")
        
        return count

    @classmethod
    def clear_expired(cls) -> int:
        """
        清理过期缓存
        
        Returns:
            清理的缓存数量
        """
        now = datetime.now()
        expired_keys = [key for key, ttl in cls._cache_ttl.items() if now >= ttl]
        count = 0
        
        for key in expired_keys:
            if cls.invalidate(key):
                count += 1
        
        if count > 0:
            log.debug(f"清理过期缓存: {count} 个")
        
        return count

    @classmethod
    def clear_all(cls) -> int:
        """
        清空所有缓存
        
        Returns:
            清理的缓存数量
        """
        count = len(cls._cache)
        cls._cache.clear()
        cls._cache_ttl.clear()
        
        if count > 0:
            log.info(f"清空所有缓存: {count} 个")
        
        return count

    @classmethod
    def get_cache_info(cls) -> Dict[str, Any]:
        """
        获取缓存信息
        
        Returns:
            缓存统计信息
        """
        now = datetime.now()
        expired_count = len([key for key, ttl in cls._cache_ttl.items() if now >= ttl])
        
        return {
            "total_keys": len(cls._cache),
            "expired_keys": expired_count,
            "active_keys": len(cls._cache) - expired_count,
            "locks_count": len(cls._locks),
            "cache_keys": list(cls._cache.keys())
        }

    @classmethod
    async def warm_up_cache(cls, cache_configs: Dict[str, Dict]) -> Dict[str, bool]:
        """
        预热缓存
        
        Args:
            cache_configs: 缓存配置字典，格式：
                {
                    "cache_key": {
                        "factory_func": callable,
                        "ttl_seconds": int
                    }
                }
        
        Returns:
            预热结果
        """
        results = {}
        
        for key, config in cache_configs.items():
            try:
                await cls.get_or_set(
                    key=key,
                    factory_func=config["factory_func"],
                    ttl_seconds=config.get("ttl_seconds", 300)
                )
                results[key] = True
                log.debug(f"缓存预热成功: {key}")
            except Exception as e:
                results[key] = False
                log.error(f"缓存预热失败 {key}: {str(e)}")
        
        return results


class CacheKeys:
    """缓存键常量"""
    
    # 应用健康状态
    APP_HEALTH = "monitor:app_health"
    
    # 业务统计
    BUSINESS_STATS = "monitor:business_stats"
    
    # 性能数据
    PERFORMANCE_STATS = "monitor:performance_stats"
    PERFORMANCE_TRENDS = "monitor:performance_trends"
    
    # 系统资源
    SYSTEM_OVERVIEW = "monitor:system_overview"
    
    # 错误分析
    ERROR_ANALYSIS = "monitor:error_analysis"
    
    # STRM任务统计
    STRM_TASK_STATS = "monitor:strm_task_stats"
    
    @classmethod
    def get_performance_key(cls, minutes: int) -> str:
        """获取性能统计的缓存键"""
        return f"{cls.PERFORMANCE_STATS}:{minutes}m"
    
    @classmethod
    def get_trends_key(cls, hours: int, interval: int) -> str:
        """获取趋势数据的缓存键"""
        return f"{cls.PERFORMANCE_TRENDS}:{hours}h:{interval}m"
    
    @classmethod
    def get_error_analysis_key(cls, hours: int) -> str:
        """获取错误分析的缓存键"""
        return f"{cls.ERROR_ANALYSIS}:{hours}h"


def determine_overall_status(db_status: Dict, strm_health: Dict, api_health: Dict) -> str:
    """
    确定整体应用状态
    
    Args:
        db_status: 数据库状态
        strm_health: STRM任务健康状态
        api_health: API健康状态
        
    Returns:
        整体状态：healthy, warning, error
    """
    if db_status.get("status") == "disconnected":
        return "error"

    if strm_health.get("status") == "error" or api_health.get("status") == "error":
        return "error"

    if strm_health.get("status") == "warning" or api_health.get("status") == "warning":
        return "warning"

    return "healthy"


# 定期清理过期缓存的后台任务
async def cache_cleanup_task():
    """缓存清理后台任务"""
    while True:
        try:
            # 每5分钟清理一次过期缓存
            await asyncio.sleep(300)
            MonitorCache.clear_expired()
        except Exception as e:
            log.error(f"缓存清理任务出错: {str(e)}")
            await asyncio.sleep(60)  # 出错后等待1分钟再重试


def start_cache_cleanup():
    """启动缓存清理任务"""
    asyncio.create_task(cache_cleanup_task())
    log.info("监控缓存清理任务已启动")


__all__ = [
    "MonitorCache",
    "CacheKeys", 
    "determine_overall_status",
    "start_cache_cleanup"
]
