"""
增强的缓存管理器
提供更智能的缓存策略和失效机制
"""

import json
import hashlib
from typing import Any, Optional, Callable, Dict, List
from datetime import datetime, timedelta
import asyncio

from app.log.log import log


class EnhancedCacheManager:
    """增强的缓存管理器"""
    
    # 内存缓存
    _memory_cache: Dict[str, Dict[str, Any]] = {}
    
    # 缓存配置
    CACHE_CONFIGS = {
        "user_tasks": {"expire": 300, "max_size": 1000},  # 5分钟
        "task_files": {"expire": 600, "max_size": 500},   # 10分钟
        "task_logs": {"expire": 180, "max_size": 200},    # 3分钟
        "system_settings": {"expire": 3600, "max_size": 10},  # 1小时
        "media_servers": {"expire": 1800, "max_size": 50},    # 30分钟
        "user_info": {"expire": 900, "max_size": 200},        # 15分钟
    }
    
    @classmethod
    def get_cache_key(cls, prefix: str, *args, **kwargs) -> str:
        """
        生成缓存键
        """
        # 将参数转换为字符串
        key_parts = [prefix]
        key_parts.extend(str(arg) for arg in args)
        key_parts.extend(f"{k}:{v}" for k, v in sorted(kwargs.items()))
        
        # 生成哈希以避免键过长
        key_string = "|".join(key_parts)
        if len(key_string) > 200:
            key_hash = hashlib.md5(key_string.encode()).hexdigest()
            return f"{prefix}:{key_hash}"
        
        return key_string.replace(" ", "_")
    
    @classmethod
    async def get_cache(cls, key: str) -> Optional[Any]:
        """
        获取缓存
        """
        try:
            if key in cls._memory_cache:
                cache_item = cls._memory_cache[key]
                
                # 检查是否过期
                if cache_item["expire_time"] > datetime.now():
                    cache_item["access_count"] += 1
                    cache_item["last_access"] = datetime.now()
                    return cache_item["data"]
                else:
                    # 过期删除
                    del cls._memory_cache[key]
            
            return None
            
        except Exception as e:
            log.warning(f"获取缓存失败: {key}, 错误: {str(e)}")
            return None
    
    @classmethod
    async def set_cache(
        cls, 
        key: str, 
        data: Any, 
        expire: Optional[int] = None,
        cache_type: str = "default"
    ) -> bool:
        """
        设置缓存
        """
        try:
            # 获取缓存配置
            config = cls.CACHE_CONFIGS.get(cache_type, {"expire": 300, "max_size": 100})
            expire_seconds = expire or config["expire"]
            max_size = config["max_size"]
            
            # 检查缓存大小限制
            if len(cls._memory_cache) >= max_size:
                await cls._cleanup_cache(cache_type, max_size)
            
            # 设置缓存
            expire_time = datetime.now() + timedelta(seconds=expire_seconds)
            cls._memory_cache[key] = {
                "data": data,
                "expire_time": expire_time,
                "create_time": datetime.now(),
                "last_access": datetime.now(),
                "access_count": 0,
                "cache_type": cache_type,
            }
            
            return True
            
        except Exception as e:
            log.error(f"设置缓存失败: {key}, 错误: {str(e)}")
            return False
    
    @classmethod
    async def get_or_set_cache(
        cls,
        key: str,
        fetch_func: Callable,
        expire: Optional[int] = None,
        cache_type: str = "default"
    ) -> Any:
        """
        获取缓存，如果不存在则执行函数并缓存结果
        """
        # 尝试获取缓存
        cached_data = await cls.get_cache(key)
        if cached_data is not None:
            return cached_data
        
        # 缓存不存在，执行函数
        try:
            data = await fetch_func()
            await cls.set_cache(key, data, expire, cache_type)
            return data
        except Exception as e:
            log.error(f"执行缓存函数失败: {key}, 错误: {str(e)}")
            raise
    
    @classmethod
    async def invalidate_cache(cls, pattern: str = None, cache_type: str = None) -> int:
        """
        失效缓存
        """
        removed_count = 0
        keys_to_remove = []
        
        for key, cache_item in cls._memory_cache.items():
            should_remove = False
            
            # 按模式匹配
            if pattern and pattern in key:
                should_remove = True
            
            # 按缓存类型匹配
            if cache_type and cache_item.get("cache_type") == cache_type:
                should_remove = True
            
            if should_remove:
                keys_to_remove.append(key)
        
        # 删除匹配的缓存
        for key in keys_to_remove:
            del cls._memory_cache[key]
            removed_count += 1
        
        if removed_count > 0:
            log.info(f"失效缓存: {removed_count} 个, 模式: {pattern}, 类型: {cache_type}")
        
        return removed_count
    
    @classmethod
    async def _cleanup_cache(cls, cache_type: str, max_size: int):
        """
        清理缓存（LRU策略）
        """
        # 获取指定类型的缓存项
        cache_items = [
            (key, item) for key, item in cls._memory_cache.items()
            if item.get("cache_type") == cache_type
        ]
        
        if len(cache_items) < max_size:
            return
        
        # 按最后访问时间排序，删除最旧的
        cache_items.sort(key=lambda x: x[1]["last_access"])
        
        # 删除最旧的25%
        remove_count = len(cache_items) // 4
        for i in range(remove_count):
            key = cache_items[i][0]
            del cls._memory_cache[key]
        
        log.info(f"清理缓存: 删除 {remove_count} 个 {cache_type} 类型的缓存项")
    
    @classmethod
    async def get_cache_stats(cls) -> Dict[str, Any]:
        """
        获取缓存统计信息
        """
        stats = {
            "total_items": len(cls._memory_cache),
            "by_type": {},
            "memory_usage_mb": 0,
        }
        
        # 按类型统计
        for cache_item in cls._memory_cache.values():
            cache_type = cache_item.get("cache_type", "unknown")
            if cache_type not in stats["by_type"]:
                stats["by_type"][cache_type] = {
                    "count": 0,
                    "total_access": 0,
                    "avg_access": 0,
                }
            
            stats["by_type"][cache_type]["count"] += 1
            stats["by_type"][cache_type]["total_access"] += cache_item.get("access_count", 0)
        
        # 计算平均访问次数
        for type_stats in stats["by_type"].values():
            if type_stats["count"] > 0:
                type_stats["avg_access"] = type_stats["total_access"] / type_stats["count"]
        
        # 估算内存使用（简单估算）
        try:
            cache_str = json.dumps(cls._memory_cache, default=str)
            stats["memory_usage_mb"] = len(cache_str.encode()) / (1024 * 1024)
        except:
            stats["memory_usage_mb"] = 0
        
        return stats
    
    @classmethod
    async def clear_expired_cache(cls) -> int:
        """
        清理过期缓存
        """
        now = datetime.now()
        expired_keys = []
        
        for key, cache_item in cls._memory_cache.items():
            if cache_item["expire_time"] <= now:
                expired_keys.append(key)
        
        for key in expired_keys:
            del cls._memory_cache[key]
        
        if expired_keys:
            log.info(f"清理过期缓存: {len(expired_keys)} 个")
        
        return len(expired_keys)


# 智能缓存装饰器
def smart_cache(
    cache_type: str = "default",
    expire: Optional[int] = None,
    key_func: Optional[Callable] = None
):
    """
    智能缓存装饰器
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = EnhancedCacheManager.get_cache_key(
                    f"{func.__name__}", *args, **kwargs
                )
            
            # 获取或设置缓存
            return await EnhancedCacheManager.get_or_set_cache(
                cache_key,
                lambda: func(*args, **kwargs),
                expire,
                cache_type
            )
        
        return wrapper
    return decorator


# 缓存失效装饰器
def cache_invalidate(patterns: List[str] = None, cache_types: List[str] = None):
    """
    缓存失效装饰器
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            result = await func(*args, **kwargs)
            
            # 失效相关缓存
            if patterns:
                for pattern in patterns:
                    await EnhancedCacheManager.invalidate_cache(pattern=pattern)
            
            if cache_types:
                for cache_type in cache_types:
                    await EnhancedCacheManager.invalidate_cache(cache_type=cache_type)
            
            return result
        
        return wrapper
    return decorator


# 定期清理过期缓存的后台任务
async def cache_cleanup_task():
    """
    定期清理过期缓存的后台任务
    """
    while True:
        try:
            await EnhancedCacheManager.clear_expired_cache()
            await asyncio.sleep(300)  # 每5分钟清理一次
        except Exception as e:
            log.error(f"缓存清理任务失败: {str(e)}")
            await asyncio.sleep(60)  # 出错后等待1分钟再试
