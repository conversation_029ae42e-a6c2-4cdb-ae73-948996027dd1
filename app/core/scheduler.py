"""
定时任务调度器模块
使用APScheduler管理数据聚合和清理任务
"""

import asyncio
from datetime import datetime
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.executors.asyncio import AsyncIOExecutor

from app.core.data_aggregator import DataAggregator
from app.log import log


class MonitorScheduler:
    """监控数据聚合调度器"""
    
    def __init__(self):
        self.scheduler = None
        self._is_running = False
    
    async def setup_scheduler(self):
        """设置调度器和定时任务"""
        if self.scheduler is not None:
            log.warning("调度器已经设置，跳过重复设置")
            return
        
        try:
            # 创建调度器
            executors = {
                'default': AsyncIOExecutor()
            }
            
            job_defaults = {
                'coalesce': True,  # 合并多个相同的任务
                'max_instances': 1,  # 每个任务最多只能有一个实例运行
                'misfire_grace_time': 300  # 任务错过执行时间后的宽限期（秒）
            }
            
            self.scheduler = AsyncIOScheduler(
                executors=executors,
                job_defaults=job_defaults,
                timezone='Asia/Shanghai'
            )
            
            # 添加小时级数据聚合任务（每小时的第5分钟执行）
            self.scheduler.add_job(
                func=self._aggregate_hourly_data_job,
                trigger=CronTrigger(minute=5),
                id="hourly_data_aggregation",
                name="小时级数据聚合",
                replace_existing=True
            )
            
            # 添加日级数据聚合任务（每天凌晨2点执行）
            self.scheduler.add_job(
                func=self._aggregate_daily_data_job,
                trigger=CronTrigger(hour=2, minute=0),
                id="daily_data_aggregation",
                name="日级数据聚合",
                replace_existing=True
            )
            
            # 添加数据清理任务（每天凌晨3点执行）
            self.scheduler.add_job(
                func=self._cleanup_old_data_job,
                trigger=CronTrigger(hour=3, minute=0),
                id="cleanup_old_data",
                name="清理过期数据",
                replace_existing=True
            )
            
            # 添加聚合数据概览任务（每天凌晨4点执行）
            self.scheduler.add_job(
                func=self._log_aggregation_summary,
                trigger=CronTrigger(hour=4, minute=0),
                id="aggregation_summary",
                name="聚合数据概览",
                replace_existing=True
            )
            
            log.info("📅 数据聚合定时任务已设置完成")
            
        except Exception as e:
            log.error(f"❌ 设置调度器失败: {str(e)}")
            raise
    
    async def start_scheduler(self):
        """启动调度器"""
        if self.scheduler is None:
            await self.setup_scheduler()
        
        if not self._is_running:
            try:
                self.scheduler.start()
                self._is_running = True
                log.info("🚀 数据聚合调度器已启动")
                
                # 记录已注册的任务
                jobs = self.scheduler.get_jobs()
                for job in jobs:
                    log.info(f"📋 已注册任务: {job.name} (ID: {job.id})")
                
            except Exception as e:
                log.error(f"❌ 启动调度器失败: {str(e)}")
                raise
    
    async def stop_scheduler(self):
        """停止调度器"""
        if self.scheduler and self._is_running:
            try:
                self.scheduler.shutdown(wait=True)
                self._is_running = False
                log.info("⏹️ 数据聚合调度器已停止")
            except Exception as e:
                log.error(f"❌ 停止调度器失败: {str(e)}")
    
    def get_scheduler_status(self) -> dict:
        """获取调度器状态"""
        if self.scheduler is None:
            return {"status": "not_initialized", "running": False, "jobs": []}
        
        jobs_info = []
        for job in self.scheduler.get_jobs():
            jobs_info.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        
        return {
            "status": "initialized",
            "running": self._is_running,
            "jobs": jobs_info
        }
    
    async def _aggregate_hourly_data_job(self):
        """小时级数据聚合任务"""
        try:
            log.info("⏰ 开始执行小时级数据聚合任务")
            result = await DataAggregator.aggregate_hourly_data()
            
            if result["status"] == "success":
                log.info(f"✅ 小时级数据聚合成功: {result['hour']}")
            elif result["status"] == "skipped":
                log.debug(f"⏭️ 小时级数据聚合跳过: {result.get('reason', 'unknown')}")
            else:
                log.error(f"❌ 小时级数据聚合失败: {result.get('error', 'unknown')}")
                
        except Exception as e:
            log.error(f"❌ 小时级数据聚合任务异常: {str(e)}")
    
    async def _aggregate_daily_data_job(self):
        """日级数据聚合任务"""
        try:
            log.info("📅 开始执行日级数据聚合任务")
            result = await DataAggregator.aggregate_daily_data()
            
            if result["status"] == "success":
                log.info(f"✅ 日级数据聚合成功: {result['date']}")
            elif result["status"] == "skipped":
                log.debug(f"⏭️ 日级数据聚合跳过: {result.get('reason', 'unknown')}")
            else:
                log.error(f"❌ 日级数据聚合失败: {result.get('error', 'unknown')}")
                
        except Exception as e:
            log.error(f"❌ 日级数据聚合任务异常: {str(e)}")
    
    async def _cleanup_old_data_job(self):
        """数据清理任务"""
        try:
            log.info("🧹 开始执行数据清理任务")
            result = await DataAggregator.cleanup_old_data()
            
            if result["status"] == "success":
                deleted = result["deleted"]
                log.info(f"✅ 数据清理成功: API日志 {deleted['api_logs']} 条, "
                        f"小时级数据 {deleted['hourly_stats']} 条, "
                        f"日级数据 {deleted['daily_stats']} 条")
            else:
                log.error(f"❌ 数据清理失败: {result.get('error', 'unknown')}")
                
        except Exception as e:
            log.error(f"❌ 数据清理任务异常: {str(e)}")
    
    async def _log_aggregation_summary(self):
        """记录聚合数据概览"""
        try:
            summary = await DataAggregator.get_aggregation_summary()
            if "error" not in summary:
                log.info(f"📊 聚合数据概览: 小时级数据 {summary['hourly_stats_count']} 条, "
                        f"日级数据 {summary['daily_stats_count']} 条, "
                        f"最新小时级: {summary['latest_hourly']}, "
                        f"最新日级: {summary['latest_daily']}")
            else:
                log.error(f"❌ 获取聚合数据概览失败: {summary['error']}")
        except Exception as e:
            log.error(f"❌ 聚合数据概览任务异常: {str(e)}")


# 全局调度器实例
monitor_scheduler = MonitorScheduler()


async def setup_monitor_scheduler():
    """设置并启动监控调度器"""
    try:
        await monitor_scheduler.setup_scheduler()
        await monitor_scheduler.start_scheduler()
        log.info("🎯 监控数据聚合调度器设置完成")
        return True
    except Exception as e:
        log.error(f"❌ 设置监控调度器失败: {str(e)}")
        return False


async def shutdown_monitor_scheduler():
    """关闭监控调度器"""
    try:
        await monitor_scheduler.stop_scheduler()
        log.info("🔚 监控数据聚合调度器已关闭")
    except Exception as e:
        log.error(f"❌ 关闭监控调度器失败: {str(e)}")


def get_scheduler_status() -> dict:
    """获取调度器状态"""
    return monitor_scheduler.get_scheduler_status()


__all__ = [
    "MonitorScheduler",
    "monitor_scheduler",
    "setup_monitor_scheduler",
    "shutdown_monitor_scheduler",
    "get_scheduler_status"
]
