/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';
import { shallowRef } from 'vue';
import { renderIcon } from '@/utils/renderIcon';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'about',
    path: '/about',
    component: 'layout.base$view.about',
    meta: {
      title: 'about',
      i18nKey: 'route.about',
      icon: 'fluent:book-information-24-regular',
      order: 99
    }
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'manage',
    path: '/manage',
    component: 'layout.base',
    meta: {
      title: 'manage',
      i18nKey: 'route.manage',
      icon: 'carbon:cloud-service-management',
      order: 9,
      roles: ['R_ADMIN']
    },
    children: [
      {
        name: 'manage_api',
        path: '/manage/api',
        component: 'view.manage_api',
        meta: {
          title: 'manage_api',
          i18nKey: 'route.manage_api'
        }
      },
      {
        name: 'manage_log',
        path: '/manage/log',
        component: 'view.manage_log',
        meta: {
          title: 'manage_log',
          i18nKey: 'route.manage_log'
        }
      },
      {
        name: 'manage_menu',
        path: '/manage/menu',
        component: 'view.manage_menu',
        meta: {
          title: 'manage_menu',
          i18nKey: 'route.manage_menu',
          icon: 'material-symbols:route',
          order: 3,
          roles: ['R_ADMIN'],
          keepAlive: true
        }
      },
      {
        name: 'manage_role',
        path: '/manage/role',
        component: 'view.manage_role',
        meta: {
          title: 'manage_role',
          i18nKey: 'route.manage_role',
          icon: 'carbon:user-role',
          order: 2,
          roles: ['R_SUPER']
        }
      },
      {
        name: 'manage_settings',
        path: '/manage/settings',
        component: 'view.manage_settings',
        meta: {
          title: 'manage_settings',
          i18nKey: 'route.manage_settings',
          icon: 'mdi:cog',
          order: 4
        }
      },
      {
        name: 'manage_user',
        path: '/manage/user',
        component: 'view.manage_user',
        meta: {
          title: 'manage_user',
          i18nKey: 'route.manage_user',
          icon: 'ic:round-manage-accounts',
          order: 1,
          roles: ['R_ADMIN']
        }
      },
      {
        name: 'manage_user-detail',
        path: '/manage/user-detail/:id',
        component: 'view.manage_user-detail',
        props: true,
        meta: {
          title: 'manage_user-detail',
          i18nKey: 'route.manage_user-detail',
          hideInMenu: true,
          roles: ['R_ADMIN'],
          activeMenu: 'manage_user'
        }
      }
    ]
  },
  {
    name: 'monitor',
    path: '/monitor',
    component: 'layout.base',
    meta: {
      title: 'monitor',
      i18nKey: 'route.monitor',
      icon: 'mdi:monitor-dashboard',
      order: 8,
      roles: ['R_ADMIN']
    },
    children: [
      {
        name: 'monitor_business',
        path: '/monitor/business',
        component: 'view.monitor_business',
        meta: {
          title: 'monitor_business',
          i18nKey: 'route.monitor_business'
        }
      },
      {
        name: 'monitor_dashboard',
        path: '/monitor/dashboard',
        component: 'view.monitor_dashboard',
        meta: {
          title: 'monitor_dashboard',
          i18nKey: 'route.monitor_dashboard',
          icon: 'mdi:view-dashboard',
          order: 1
        }
      },
      {
        name: 'monitor_errors',
        path: '/monitor/errors',
        component: 'view.monitor_errors',
        meta: {
          title: 'monitor_errors',
          i18nKey: 'route.monitor_errors'
        }
      },
      {
        name: 'monitor_performance',
        path: '/monitor/performance',
        component: 'view.monitor_performance',
        meta: {
          title: 'monitor_performance',
          i18nKey: 'route.monitor_performance'
        }
      },
      {
        name: 'monitor_system',
        path: '/monitor/system',
        component: 'view.monitor_system',
        meta: {
          title: 'monitor_system',
          i18nKey: 'route.monitor_system',
          icon: 'mdi:server',
          order: 2
        }
      },
      {
        name: 'monitor_tasks',
        path: '/monitor/tasks',
        component: 'view.monitor_tasks',
        meta: {
          title: 'monitor_tasks',
          i18nKey: 'route.monitor_tasks'
        }
      }
    ]
  },
  {
    name: 'strm',
    path: '/strm',
    component: 'layout.base',
    meta: {
      title: 'STRM管理',
      i18nKey: 'route.strm',
      order: 6
    },
    children: [
      {
        name: 'strm_generate',
        path: '/strm/generate',
        component: 'view.strm_generate',
        meta: {
          title: 'STRM生成',
          i18nKey: 'route.strm_generate',
          order: 2
        }
      },
      {
        name: 'strm_history',
        path: '/strm/history',
        component: 'view.strm_history',
        meta: {
          title: '上传历史',
          i18nKey: 'route.strm_history',
          order: 3
        }
      },
      {
        name: 'strm_tasks',
        path: '/strm/tasks',
        component: 'view.strm_tasks',
        meta: {
          title: '任务管理',
          i18nKey: 'route.strm_tasks',
          order: 4
        }
      },
      {
        name: 'strm_upload',
        path: '/strm/upload',
        component: 'view.strm_upload',
        meta: {
          title: '文件上传',
          i18nKey: 'route.strm_upload',
          order: 1
        }
      }
    ]
  },
  {
    name: 'user-center',
    path: '/user-center',
    component: 'layout.base$view.user-center',
    meta: {
      title: 'user-center',
      i18nKey: 'route.user-center',
      hideInMenu: true
    }
  }
];
