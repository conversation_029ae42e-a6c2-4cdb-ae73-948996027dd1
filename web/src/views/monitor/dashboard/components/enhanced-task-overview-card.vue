<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { NCard, NStatistic, NTag, NButton, NSpace, NIcon, NGrid, NGridItem, NProgress } from 'naive-ui';
import { useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import { useEcharts } from '@/hooks/common/echarts';

interface Props {
  businessData?: Api.Monitor.BusinessStatsData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  businessData: null,
  loading: false
});

const router = useRouter();

// 任务状态饼图
const { domRef: taskPieRef, updateOptions: updateTaskPie } = useEcharts(() => ({
  title: {
    text: '任务状态分布',
    left: 'center',
    top: '10%',
    fontSize: 14,
    fontWeight: 'bold'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'horizontal',
    bottom: '5%',
    left: 'center',
    itemWidth: 12,
    itemHeight: 12,
    fontSize: 12
  },
  series: [{
    name: '任务状态',
    type: 'pie',
    radius: ['40%', '70%'],
    center: ['50%', '55%'],
    avoidLabelOverlap: false,
    itemStyle: {
      borderRadius: 8,
      borderColor: '#fff',
      borderWidth: 2
    },
    label: {
      show: false,
      position: 'center'
    },
    emphasis: {
      label: {
        show: true,
        fontSize: 16,
        fontWeight: 'bold'
      },
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    },
    labelLine: {
      show: false
    },
    data: [
      { value: 0, name: '已完成', itemStyle: { color: '#52c41a' } },
      { value: 0, name: '运行中', itemStyle: { color: '#1890ff' } },
      { value: 0, name: '失败', itemStyle: { color: '#ff4d4f' } },
      { value: 0, name: '等待中', itemStyle: { color: '#faad14' } }
    ]
  }]
}));

// 成功率仪表盘
const { domRef: successRateRef, updateOptions: updateSuccessRate } = useEcharts(() => ({
  series: [{
    type: 'gauge',
    startAngle: 180,
    endAngle: 0,
    center: ['50%', '75%'],
    radius: '90%',
    min: 0,
    max: 100,
    splitNumber: 5,
    axisLine: {
      lineStyle: {
        width: 6,
        color: [
          [0.3, '#ff4d4f'],
          [0.7, '#faad14'],
          [1, '#52c41a']
        ]
      }
    },
    pointer: {
      icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
      length: '12%',
      width: 20,
      offsetCenter: [0, '-60%'],
      itemStyle: {
        color: 'auto'
      }
    },
    axisTick: {
      length: 12,
      lineStyle: {
        color: 'auto',
        width: 2
      }
    },
    splitLine: {
      length: 20,
      lineStyle: {
        color: 'auto',
        width: 5
      }
    },
    axisLabel: {
      color: '#464646',
      fontSize: 12,
      distance: -60,
      rotate: 'tangential',
      formatter: function (value: number) {
        if (value === 0) return '0%';
        if (value === 50) return '50%';
        if (value === 100) return '100%';
        return '';
      }
    },
    title: {
      offsetCenter: [0, '-10%'],
      fontSize: 14,
      fontWeight: 'bold',
      color: '#464646'
    },
    detail: {
      fontSize: 20,
      fontWeight: 'bold',
      offsetCenter: [0, '-35%'],
      valueAnimation: true,
      formatter: function (value: number) {
        return Math.round(value) + '%';
      },
      color: 'auto'
    },
    data: [{
      value: 0,
      name: '成功率'
    }]
  }]
}));

// 计算任务统计
const taskStats = computed(() => {
  if (!props.businessData) {
    return {
      total: 0,
      completed: 0,
      running: 0,
      failed: 0,
      pending: 0,
      successRate: 0
    };
  }

  const tasks = props.businessData.strm_tasks;
  const failed = Math.max(0, tasks.total - tasks.completed - tasks.running);
  const pending = Math.max(0, tasks.total - tasks.completed - tasks.running - failed);

  // 处理成功率格式：如果值小于等于1，认为是小数格式(0-1)，需要乘以100；否则认为已经是百分比格式(0-100)
  const rawSuccessRate = tasks.success_rate || 0;
  const successRate = rawSuccessRate <= 1 ? rawSuccessRate * 100 : rawSuccessRate;

  return {
    total: tasks.total,
    completed: tasks.completed,
    running: tasks.running,
    failed: failed,
    pending: pending,
    successRate: Math.min(successRate, 100) // 确保不超过100%
  };
});

// 成功率状态
const successRateStatus = computed(() => {
  const rate = taskStats.value.successRate;
  if (rate >= 95) return { type: 'success', color: '#52c41a', text: '优秀' };
  if (rate >= 80) return { type: 'warning', color: '#faad14', text: '良好' };
  if (rate >= 60) return { type: 'warning', color: '#faad14', text: '一般' };
  return { type: 'error', color: '#ff4d4f', text: '较差' };
});

// 监听数据变化，更新图表
watch(() => props.businessData, () => {
  if (props.businessData) {
    const stats = taskStats.value;

    // 更新饼图
    updateTaskPie(prev => ({
      ...prev,
      series: [{
        ...prev.series[0],
        data: [
          { value: stats.completed, name: '已完成', itemStyle: { color: '#52c41a' } },
          { value: stats.running, name: '运行中', itemStyle: { color: '#1890ff' } },
          { value: stats.failed, name: '失败', itemStyle: { color: '#ff4d4f' } },
          { value: stats.pending, name: '等待中', itemStyle: { color: '#faad14' } }
        ].filter(item => item.value > 0)
      }]
    }));

    // 更新成功率仪表盘
    updateSuccessRate(prev => ({
      ...prev,
      series: [{
        ...prev.series[0],
        data: [{
          value: stats.successRate,
          name: '成功率'
        }]
      }]
    }));
  }
}, { immediate: true });

function goToTaskDetail() {
  router.push('/monitor/tasks');
}
</script>

<template>
  <NCard class="enhanced-task-overview-card">
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <Icon icon="mdi:format-list-checks" class="text-2xl text-blue-500" />
          <span class="text-lg font-semibold">任务执行概览</span>
        </div>
        <NButton text @click="goToTaskDetail">
          <template #icon>
            <Icon icon="mdi:arrow-right" />
          </template>
          查看详情
        </NButton>
      </div>
    </template>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 任务状态分布饼图 -->
      <div class="chart-container">
        <div ref="taskPieRef" class="task-pie-chart"></div>
      </div>

      <!-- 成功率仪表盘 -->
      <div class="chart-container">
        <div ref="successRateRef" class="success-rate-chart"></div>
      </div>
    </div>

    <!-- 任务统计卡片 -->
    <div class="mt-6">
      <NGrid :cols="4" :x-gap="16" :y-gap="16">
        <NGridItem>
          <div class="stat-card total">
            <div class="stat-icon">
              <Icon icon="mdi:format-list-bulleted" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ taskStats.total }}</div>
              <div class="stat-label">总任务数</div>
            </div>
          </div>
        </NGridItem>

        <NGridItem>
          <div class="stat-card completed">
            <div class="stat-icon">
              <Icon icon="mdi:check-circle" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ taskStats.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </NGridItem>

        <NGridItem>
          <div class="stat-card running">
            <div class="stat-icon">
              <Icon icon="mdi:play-circle" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ taskStats.running }}</div>
              <div class="stat-label">运行中</div>
            </div>
          </div>
        </NGridItem>

        <NGridItem>
          <div class="stat-card failed">
            <div class="stat-icon">
              <Icon icon="mdi:alert-circle" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ taskStats.failed }}</div>
              <div class="stat-label">失败</div>
            </div>
          </div>
        </NGridItem>
      </NGrid>
    </div>

    <!-- 成功率进度条 -->
    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
      <div class="flex justify-between items-center mb-2">
        <span class="text-sm font-medium text-gray-700">整体成功率</span>
        <NTag :type="successRateStatus.type" size="small">
          {{ successRateStatus.text }} - {{ taskStats.successRate.toFixed(1) }}%
        </NTag>
      </div>
      <NProgress
        :percentage="taskStats.successRate"
        :color="successRateStatus.color"
        :show-indicator="false"
        :height="8"
      />
    </div>

    <!-- 快速操作 -->
    <div class="mt-6 pt-4 border-t border-gray-200">
      <NSpace justify="center">
        <NButton @click="goToTaskDetail" type="primary" ghost>
          <template #icon>
            <Icon icon="mdi:chart-pie" />
          </template>
          查看任务详情
        </NButton>
      </NSpace>
    </div>
  </NCard>
</template>

<style scoped>
.enhanced-task-overview-card {
  height: 100%;
  transition: all 0.3s ease;
}

.enhanced-task-overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.chart-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.task-pie-chart,
.success-rate-chart {
  width: 100%;
  height: 200px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card.completed {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stat-card.running {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.stat-card.failed {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.stat-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  opacity: 0.9;
  margin-top: 2px;
}

@media (max-width: 1024px) {
  .grid {
    grid-template-columns: 1fr;
  }

  .task-pie-chart,
  .success-rate-chart {
    height: 150px;
  }
}

@media (max-width: 768px) {
  .stat-card {
    padding: 12px;
  }

  .stat-value {
    font-size: 1.25rem;
  }

  .stat-icon {
    width: 32px;
    height: 32px;
    font-size: 1.25rem;
  }
}
</style>
