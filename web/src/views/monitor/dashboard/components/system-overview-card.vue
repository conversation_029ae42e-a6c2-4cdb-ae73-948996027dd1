<script setup lang="ts">
import { ref, computed } from 'vue';
import { NCard, NStatistic, NTag, NButton, NSpace, NIcon } from 'naive-ui';
import { useRouter } from 'vue-router';
import { ArrowRight } from '@vicons/carbon';

interface Props {
  healthData?: Api.Monitor.AppHealthData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  healthData: null,
  loading: false
});

const router = useRouter();

// 计算系统状态
const systemStatus = computed(() => {
  if (!props.healthData) return { type: 'default', text: '未知' };

  const status = props.healthData.app_info.status;
  switch (status) {
    case 'healthy':
      return { type: 'success', text: '健康' };
    case 'warning':
      return { type: 'warning', text: '警告' };
    case 'error':
      return { type: 'error', text: '错误' };
    default:
      return { type: 'default', text: '未知' };
  }
});

// 计算运行时长
const uptimeText = computed(() => {
  if (!props.healthData) return '未知';

  const uptime = props.healthData.app_info.uptime;
  const hours = Math.floor(uptime / 3600);
  const minutes = Math.floor((uptime % 3600) / 60);

  if (hours > 24) {
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return `${days}天${remainingHours}小时`;
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else {
    return `${minutes}分钟`;
  }
});

// 数据库状态
const dbStatus = computed(() => {
  if (!props.healthData) return { type: 'default', text: '未知' };

  const status = props.healthData.database.status;
  return status === 'connected'
    ? { type: 'success', text: '已连接' }
    : { type: 'error', text: '未连接' };
});

function goToSystemDetail() {
  router.push('/monitor/system');
}
</script>

<template>
  <NCard title="系统状态概览" class="overview-card system-overview-card">
    <template #header-extra>
      <NButton text @click="goToSystemDetail">
        <template #icon>
          <NIcon><ArrowRight /></NIcon>
        </template>
        查看详情
      </NButton>
    </template>

    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <!-- 系统状态 -->
      <div class="text-center">
        <div class="mb-2">
          <NTag :type="systemStatus.type" size="large">
            {{ systemStatus.text }}
          </NTag>
        </div>
        <div class="text-sm text-gray-600">系统状态</div>
      </div>

      <!-- 运行时长 -->
      <div class="text-center">
        <NStatistic
          :value="uptimeText"
          label="运行时长"
          class="text-center"
        />
      </div>

      <!-- 数据库状态 -->
      <div class="text-center">
        <div class="mb-2">
          <NTag :type="dbStatus.type" size="large">
            {{ dbStatus.text }}
          </NTag>
        </div>
        <div class="text-sm text-gray-600">数据库</div>
      </div>

      <!-- 平台信息 -->
      <div class="text-center">
        <NStatistic
          :value="healthData?.app_info.platform || '未知'"
          label="运行平台"
          class="text-center"
        />
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="mt-6 pt-4 border-t border-gray-200">
      <NSpace justify="center">
        <NButton @click="goToSystemDetail" type="primary" ghost>
          查看系统详情
        </NButton>
      </NSpace>
    </div>
  </NCard>
</template>

<style scoped>
.system-overview-card {
  height: 100%;
}

.system-overview-card :deep(.n-statistic-value) {
  font-size: 1.25rem;
  font-weight: 600;
}

.system-overview-card :deep(.n-statistic-label) {
  font-size: 0.875rem;
  color: #6b7280;
}
</style>
