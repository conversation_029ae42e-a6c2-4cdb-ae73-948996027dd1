<script setup lang="ts">
import { ref, computed } from 'vue';
import { <PERSON>ard, NStatistic, NProgress, NButton, NSpace, NIcon, NTag } from 'naive-ui';
import { useRouter } from 'vue-router';
import { ArrowRight } from '@vicons/carbon';

interface Props {
  performanceData?: Api.Monitor.PerformanceData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  performanceData: null,
  loading: false
});

const router = useRouter();

// 计算性能指标
const performanceStats = computed(() => {
  if (!props.performanceData || !props.performanceData.api_performance) {
    return {
      avgResponseTime: 0,
      requestCount: 0,
      errorRate: 0,
      requestsPerMinute: 0
    };
  }

  const api = props.performanceData.api_performance;
  return {
    avgResponseTime: api.avg_response_time || 0,
    requestCount: api.request_count || 0,
    errorRate: (api.error_rate || 0) * 100,
    requestsPerMinute: api.requests_per_minute || 0
  };
});

// 响应时间状态
const responseTimeStatus = computed(() => {
  const time = performanceStats.value.avgResponseTime;
  if (time <= 0.5) return { type: 'success', text: '优秀' };
  if (time <= 1.0) return { type: 'warning', text: '良好' };
  if (time <= 2.0) return { type: 'warning', text: '一般' };
  return { type: 'error', text: '较慢' };
});

// 错误率状态
const errorRateStatus = computed(() => {
  const rate = performanceStats.value.errorRate;
  if (rate <= 1) return { type: 'success', color: '#52c41a' };
  if (rate <= 5) return { type: 'warning', color: '#faad14' };
  return { type: 'error', color: '#ff4d4f' };
});

function goToPerformanceDetail() {
  router.push('/monitor/performance');
}
</script>

<template>
  <NCard title="性能概览" class="overview-card performance-overview-card">
    <template #header-extra>
      <NButton text @click="goToPerformanceDetail">
        <template #icon>
          <NIcon><ArrowRight /></NIcon>
        </template>
        查看详情
      </NButton>
    </template>

    <div class="space-y-4">
      <!-- 性能指标 -->
      <div class="grid grid-cols-2 gap-4">
        <div class="text-center">
          <div class="mb-2">
            <span class="text-2xl font-bold text-gray-900">
              {{ performanceStats.avgResponseTime.toFixed(2) }}s
            </span>
          </div>
          <div class="text-sm text-gray-600">平均响应时间</div>
          <NTag :type="responseTimeStatus.type" size="small" class="mt-1">
            {{ responseTimeStatus.text }}
          </NTag>
        </div>

        <div class="text-center">
          <NStatistic
            :value="performanceStats.requestCount"
            label="总请求数"
            class="text-center"
          />
        </div>
      </div>

      <!-- 错误率 -->
      <div class="space-y-2">
        <div class="flex justify-between items-center">
          <span class="text-sm font-medium text-gray-700">错误率</span>
          <NTag :type="errorRateStatus.type" size="small">
            {{ performanceStats.errorRate.toFixed(2) }}%
          </NTag>
        </div>
        <NProgress
          :percentage="Math.min(performanceStats.errorRate, 10) * 10"
          :color="errorRateStatus.color"
          :show-indicator="false"
        />
      </div>

      <!-- 每分钟请求数 -->
      <div class="text-center">
        <NStatistic
          :value="performanceStats.requestsPerMinute.toFixed(1)"
          label="每分钟请求数"
          class="text-center"
        />
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="mt-6 pt-4 border-t border-gray-200">
      <NSpace justify="center">
        <NButton @click="goToPerformanceDetail" type="primary" ghost>
          查看性能详情
        </NButton>
      </NSpace>
    </div>
  </NCard>
</template>

<style scoped>
.performance-overview-card {
  height: 100%;
}

.performance-overview-card :deep(.n-statistic-value) {
  font-size: 1.25rem;
  font-weight: 600;
}

.performance-overview-card :deep(.n-statistic-label) {
  font-size: 0.875rem;
  color: #6b7280;
}
</style>
