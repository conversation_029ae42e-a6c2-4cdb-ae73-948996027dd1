<script setup lang="ts">
import { ref, computed } from 'vue';
import { NCard, NStatistic, NButton, NSpace, NIcon, NTag, NAlert } from 'naive-ui';
import { useRouter } from 'vue-router';
import { ArrowRight, Warning } from '@vicons/carbon';

interface Props {
  errorData?: any | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  errorData: null,
  loading: false
});

const router = useRouter();

// 计算错误统计（模拟数据，实际应该从API获取）
const errorStats = computed(() => {
  // 这里应该从实际的错误分析数据中获取
  // 暂时使用模拟数据
  return {
    totalErrors: 12,
    apiErrors: 8,
    taskErrors: 4,
    errorRate: 2.3,
    trend: 'down' // up, down, stable
  };
});

// 错误级别状态
const errorLevelStatus = computed(() => {
  const rate = errorStats.value.errorRate;
  if (rate <= 1) return { type: 'success', text: '低', color: '#52c41a' };
  if (rate <= 5) return { type: 'warning', text: '中', color: '#faad14' };
  return { type: 'error', text: '高', color: '#ff4d4f' };
});

// 趋势状态
const trendStatus = computed(() => {
  const trend = errorStats.value.trend;
  switch (trend) {
    case 'up':
      return { type: 'error', text: '上升', icon: '↗' };
    case 'down':
      return { type: 'success', text: '下降', icon: '↘' };
    default:
      return { type: 'info', text: '稳定', icon: '→' };
  }
});

function goToErrorDetail() {
  router.push('/monitor/errors');
}
</script>

<template>
  <NCard title="错误概览" class="overview-card error-overview-card">
    <template #header-extra>
      <NButton text @click="goToErrorDetail">
        <template #icon>
          <NIcon><ArrowRight /></NIcon>
        </template>
        查看详情
      </NButton>
    </template>

    <div class="space-y-4">
      <!-- 错误统计 -->
      <div class="grid grid-cols-2 gap-4">
        <div class="text-center">
          <div class="mb-2">
            <span class="text-2xl font-bold text-red-600">
              {{ errorStats.totalErrors }}
            </span>
          </div>
          <div class="text-sm text-gray-600">总错误数</div>
        </div>

        <div class="text-center">
          <div class="mb-2">
            <NTag :type="errorLevelStatus.type" size="large">
              {{ errorStats.errorRate }}%
            </NTag>
          </div>
          <div class="text-sm text-gray-600">错误率</div>
        </div>
      </div>

      <!-- 错误分类 -->
      <div class="grid grid-cols-2 gap-4">
        <div class="text-center">
          <NStatistic
            :value="errorStats.apiErrors"
            label="API错误"
            class="text-center"
          />
        </div>
        <div class="text-center">
          <NStatistic
            :value="errorStats.taskErrors"
            label="任务错误"
            class="text-center"
          />
        </div>
      </div>

      <!-- 趋势指示 -->
      <div class="flex justify-center items-center space-x-2">
        <span class="text-sm text-gray-600">趋势:</span>
        <NTag :type="trendStatus.type" size="small">
          {{ trendStatus.icon }} {{ trendStatus.text }}
        </NTag>
      </div>

      <!-- 警告提示 -->
      <div v-if="errorStats.errorRate > 5" class="mt-4">
        <NAlert type="warning" size="small">
          <template #icon>
            <NIcon><Warning /></NIcon>
          </template>
          错误率较高，建议及时处理
        </NAlert>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="mt-6 pt-4 border-t border-gray-200">
      <NSpace justify="center">
        <NButton @click="goToErrorDetail" type="primary" ghost>
          查看错误详情
        </NButton>
      </NSpace>
    </div>
  </NCard>
</template>

<style scoped>
.error-overview-card {
  height: 100%;
}

.error-overview-card :deep(.n-statistic-value) {
  font-size: 1.25rem;
  font-weight: 600;
}

.error-overview-card :deep(.n-statistic-label) {
  font-size: 0.875rem;
  color: #6b7280;
}
</style>
