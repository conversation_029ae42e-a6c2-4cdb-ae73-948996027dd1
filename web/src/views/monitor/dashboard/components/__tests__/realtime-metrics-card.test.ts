import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import { NConfigProvider } from 'naive-ui';
import RealtimeMetricsCard from '../realtime-metrics-card.vue';

// Mock data
const mockHealthData: Api.Monitor.AppHealthData = {
  app_info: {
    name: 'test-app',
    version: '1.0.0',
    start_time: '2024-01-01T00:00:00Z',
    uptime: 3600,
    status: 'healthy',
    platform: 'linux',
    python_version: '3.9.0'
  },
  database: {
    status: 'connected',
    size: 1024,
    connection_pool: {
      active: 5,
      idle: 10,
      total: 15
    }
  },
  strm_tasks: {
    status: 'healthy',
    total_tasks: 100,
    running_tasks: 5,
    failed_tasks: 2,
    success_rate: 0.95
  },
  api_performance: {
    status: 'healthy',
    avg_response_time: 0.5,
    request_count_24h: 1000,
    error_rate: 0.01
  }
};

const mockBusinessData: Api.Monitor.BusinessStatsData = {
  strm_tasks: {
    total: 100,
    completed: 90,
    failed: 5,
    running: 3,
    pending: 2,
    success_rate: 0.95, // 使用0-1格式，测试自动转换
    avg_processing_time: 120
  },
  user_activity: {
    total_users: 50,
    active_today: 20,
    login_count_today: 30,
    active_users_week: 40
  },
  api_requests: {
    total_requests_today: 1000,
    avg_response_time: 0.5,
    error_count: 10,
    top_endpoints: []
  }
};

const mockPerformanceData: Api.Monitor.PerformanceData = {
  api_performance: {
    avg_response_time: 0.5,
    request_count: 1000,
    error_rate: 0.01,
    requests_per_minute: 50,
    slowest_endpoints: [],
    status_code_distribution: {}
  },
  database_performance: {
    connection_status: 'connected',
    query_stats: {
      avg_query_time: 0.1,
      slow_queries: 2,
      total_queries: 500
    }
  },
  system_resources: {
    available: true,
    memory_usage: 60,
    cpu_usage: 45,
    disk_usage: 70
  }
};

const mockSystemData: Api.Monitor.SystemOverviewData = {
  available: true,
  cpu: {
    percent: 45,
    cores_physical: 4,
    cores_logical: 8,
    load_avg: [1.0, 1.2, 1.1]
  },
  memory: {
    total: **********,
    available: **********,
    used: **********,
    percent: 60,
    free: **********,
    buffers: 0,
    cached: 0
  }
};

describe('RealtimeMetricsCard', () => {
  it('renders correctly with mock data', () => {
    const wrapper = mount(RealtimeMetricsCard, {
      props: {
        healthData: mockHealthData,
        businessData: mockBusinessData,
        performanceData: mockPerformanceData,
        systemData: mockSystemData,
        loading: false
      },
      global: {
        components: {
          NConfigProvider
        }
      }
    });

    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.realtime-metrics-card').exists()).toBe(true);
  });

  it('displays correct number of metrics', () => {
    const wrapper = mount(RealtimeMetricsCard, {
      props: {
        healthData: mockHealthData,
        businessData: mockBusinessData,
        performanceData: mockPerformanceData,
        systemData: mockSystemData,
        loading: false
      },
      global: {
        components: {
          NConfigProvider
        }
      }
    });

    // Should display 6 core metrics
    const metricItems = wrapper.findAll('.metric-item');
    expect(metricItems.length).toBe(6);
  });

  it('handles null data gracefully', () => {
    const wrapper = mount(RealtimeMetricsCard, {
      props: {
        healthData: null,
        businessData: null,
        performanceData: null,
        systemData: null,
        loading: false
      },
      global: {
        components: {
          NConfigProvider
        }
      }
    });

    expect(wrapper.exists()).toBe(true);
    // Should still render metrics with default values
    const metricItems = wrapper.findAll('.metric-item');
    expect(metricItems.length).toBe(6);
  });
});
