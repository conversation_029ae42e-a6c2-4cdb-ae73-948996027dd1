<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { NCard, NTag, NButton, NGrid, NGridItem, NProgress, NEmpty } from 'naive-ui';
import { useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import { fetchErrorAnalysis } from '@/service/api';

interface Props {
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const router = useRouter();
const errorData = ref<Api.Monitor.ErrorAnalysisData | null>(null);
const dataLoading = ref(true);

// 计算错误统计
const errorStats = computed(() => {
  if (!errorData.value?.error_summary) {
    return {
      totalErrors: 0,
      errorRate: 0,
      criticalErrors: 0,
      topErrorType: '无',
      errorTrend: 'stable'
    };
  }

  const data = errorData.value;
  const errorsByType = data.failed_tasks?.by_type || [];

  // 找出最多的错误类型
  const topError = errorsByType.reduce((max, current) =>
    current.count > max.count ? current : max,
    { type: '无', count: 0 }
  );

  // 简化趋势计算
  const trendData = data.trend_data || [];
  const recent = trendData.slice(-3);
  const earlier = trendData.slice(-6, -3);

  const recentAvg = recent.length > 0 ?
    recent.reduce((sum, item) => sum + item.api_error_count + item.task_failure_count, 0) / recent.length : 0;
  const earlierAvg = earlier.length > 0 ?
    earlier.reduce((sum, item) => sum + item.api_error_count + item.task_failure_count, 0) / earlier.length : 0;

  let errorTrend = 'stable';
  if (recentAvg > earlierAvg * 1.2) errorTrend = 'increasing';
  else if (recentAvg < earlierAvg * 0.8) errorTrend = 'decreasing';

  return {
    totalErrors: data.error_summary.total_errors,
    errorRate: data.error_summary.error_rate * 100,
    criticalErrors: data.failed_tasks?.total || 0,
    topErrorType: topError.type,
    errorTrend
  };
});

// 获取趋势状态
function getTrendStatus(trend: string) {
  const statusMap = {
    increasing: { type: 'error' as const, text: '上升', icon: 'mdi:trending-up' },
    decreasing: { type: 'success' as const, text: '下降', icon: 'mdi:trending-down' },
    stable: { type: 'info' as const, text: '稳定', icon: 'mdi:trending-neutral' }
  };
  return statusMap[trend as keyof typeof statusMap] || statusMap.stable;
}

// 获取错误率状态
function getErrorRateStatus(rate: number) {
  if (rate <= 1) return { type: 'success' as const, text: '正常' };
  if (rate <= 5) return { type: 'warning' as const, text: '注意' };
  return { type: 'error' as const, text: '异常' };
}

// 简化的模拟数据
function generateMockData() {
  const totalErrors = Math.floor(Math.random() * 50) + 10;
  const totalRequests = Math.floor(Math.random() * 1000) + 500;

  return {
    error_summary: {
      total_errors: totalErrors,
      total_requests: totalRequests,
      error_rate: totalErrors / totalRequests,
      top_errors: [],
      top_error_endpoints: []
    },
    failed_tasks: {
      total: Math.floor(Math.random() * 10),
      total_tasks: Math.floor(Math.random() * 50) + 20,
      failure_rate: Math.random() * 0.1,
      by_type: [
        { type: 'STRM生成', count: Math.floor(Math.random() * 5) },
        { type: '资源下载', count: Math.floor(Math.random() * 3) }
      ]
    },
    trend_data: [],
    analysis_period_hours: 24
  };
}

// 加载错误数据
async function loadErrorData() {
  try {
    dataLoading.value = true;
    const response = await fetchErrorAnalysis({ hours: 24 });
    errorData.value = response.data;
  } catch (error) {
    console.warn('获取错误数据失败，使用模拟数据:', error);
    errorData.value = generateMockData() as any;
  } finally {
    dataLoading.value = false;
  }
}

onMounted(() => {
  loadErrorData();
});

function goToErrorDetail() {
  router.push('/monitor/errors');
}
</script>

<template>
  <NCard class="error-overview-card">
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <Icon icon="mdi:alert-circle" class="text-xl text-red-500" />
          <span class="font-semibold">错误分析</span>
        </div>
        <NButton text @click="goToErrorDetail" size="small">
          <template #icon>
            <Icon icon="mdi:arrow-right" />
          </template>
          详情
        </NButton>
      </div>
    </template>

    <div v-if="dataLoading" class="text-center py-8">
      <Icon icon="mdi:loading" class="text-2xl text-gray-400 animate-spin mb-2" />
      <p class="text-sm text-gray-500">加载中...</p>
    </div>

    <div v-else-if="!errorData" class="text-center py-8">
      <NEmpty description="暂无数据" size="small" />
    </div>

    <div v-else class="space-y-4">
      <!-- 错误统计 -->
      <NGrid :cols="2" :x-gap="12" :y-gap="12">
        <NGridItem>
          <div class="stat-card">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-lg font-bold text-red-600">{{ errorStats.totalErrors }}</div>
                <div class="text-xs text-gray-500">总错误数</div>
              </div>
              <Icon icon="mdi:alert-circle-outline" class="text-xl text-red-400" />
            </div>
          </div>
        </NGridItem>

        <NGridItem>
          <div class="stat-card">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-lg font-bold" :class="getErrorRateStatus(errorStats.errorRate).type === 'error' ? 'text-red-600' : 'text-green-600'">
                  {{ errorStats.errorRate.toFixed(1) }}%
                </div>
                <div class="text-xs text-gray-500">错误率</div>
              </div>
              <Icon icon="mdi:percent" class="text-xl text-orange-400" />
            </div>
          </div>
        </NGridItem>

        <NGridItem>
          <div class="stat-card">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-lg font-bold text-orange-600">{{ errorStats.criticalErrors }}</div>
                <div class="text-xs text-gray-500">严重错误</div>
              </div>
              <Icon icon="mdi:alert" class="text-xl text-orange-400" />
            </div>
          </div>
        </NGridItem>

        <NGridItem>
          <div class="stat-card">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-lg font-bold text-blue-600">{{ errorStats.topErrorType }}</div>
                <div class="text-xs text-gray-500">主要类型</div>
              </div>
              <Icon :icon="getTrendStatus(errorStats.errorTrend).icon" class="text-xl text-blue-400" />
            </div>
          </div>
        </NGridItem>
      </NGrid>

      <!-- 状态指示 -->
      <div class="p-3 bg-gray-50 rounded-lg">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-medium">状态评估</span>
          <div class="flex gap-1">
            <NTag :type="getErrorRateStatus(errorStats.errorRate).type" size="small">
              {{ getErrorRateStatus(errorStats.errorRate).text }}
            </NTag>
            <NTag :type="getTrendStatus(errorStats.errorTrend).type" size="small">
              {{ getTrendStatus(errorStats.errorTrend).text }}
            </NTag>
          </div>
        </div>
        <NProgress
          :percentage="Math.min(errorStats.errorRate * 10, 100)"
          :color="getErrorRateStatus(errorStats.errorRate).type === 'error' ? '#ff4d4f' : '#52c41a'"
          :show-indicator="false"
          :height="4"
        />
      </div>
    </div>
  </NCard>
</template>

<style scoped>
.error-overview-card {
  height: 100%;
}

.stat-card {
  padding: 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.stat-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
