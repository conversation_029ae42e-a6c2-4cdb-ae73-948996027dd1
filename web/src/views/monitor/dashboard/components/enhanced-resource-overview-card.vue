<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { NCard, NStatistic, NTag, NButton, NSpace, NIcon, NGrid, NGridItem, NProgress } from 'naive-ui';
import { useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import { useEcharts } from '@/hooks/common/echarts';

interface Props {
  systemData?: Api.Monitor.SystemOverviewData | null;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  systemData: null,
  loading: false
});

const router = useRouter();

// 资源使用率环形图
const { domRef: resourceRingRef, updateOptions: updateResourceRing } = useEcharts(() => ({
  title: {
    text: '系统资源使用率',
    left: 'center',
    top: '5%',
    textStyle: {
      fontSize: 14,
      fontWeight: 'bold'
    }
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c}%'
  },
  legend: {
    orient: 'horizontal',
    bottom: '5%',
    left: 'center',
    itemWidth: 12,
    itemHeight: 12,
    textStyle: {
      fontSize: 12
    }
  },
  series: [{
    name: '资源使用率',
    type: 'pie',
    radius: ['40%', '70%'],
    center: ['50%', '55%'],
    avoidLabelOverlap: false,
    itemStyle: {
      borderRadius: 8,
      borderColor: '#fff',
      borderWidth: 2
    },
    label: {
      show: true,
      position: 'outside',
      formatter: '{b}: {c}%',
      fontSize: 10
    },
    emphasis: {
      label: {
        show: true,
        fontSize: 12,
        fontWeight: 'bold'
      },
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    },
    data: [
      { value: 0, name: 'CPU', itemStyle: { color: '#1890ff' } },
      { value: 0, name: '内存', itemStyle: { color: '#52c41a' } },
      { value: 0, name: '磁盘', itemStyle: { color: '#faad14' } }
    ]
  }]
}));

// CPU使用率仪表盘
const { domRef: cpuGaugeRef, updateOptions: updateCpuGauge } = useEcharts(() => ({
  series: [{
    type: 'gauge',
    startAngle: 180,
    endAngle: 0,
    center: ['50%', '75%'],
    radius: '90%',
    min: 0,
    max: 100,
    splitNumber: 5,
    axisLine: {
      lineStyle: {
        width: 6,
        color: [
          [0.6, '#52c41a'],
          [0.8, '#faad14'],
          [1, '#ff4d4f']
        ]
      }
    },
    pointer: {
      icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
      length: '12%',
      width: 20,
      offsetCenter: [0, '-60%'],
      itemStyle: {
        color: 'auto'
      }
    },
    axisTick: {
      length: 12,
      lineStyle: {
        color: 'auto',
        width: 2
      }
    },
    splitLine: {
      length: 20,
      lineStyle: {
        color: 'auto',
        width: 5
      }
    },
    axisLabel: {
      color: '#464646',
      fontSize: 10,
      distance: -50,
      rotate: 'tangential',
      formatter: function (value: number) {
        if (value === 0) return '0%';
        if (value === 50) return '50%';
        if (value === 100) return '100%';
        return '';
      }
    },
    title: {
      offsetCenter: [0, '-10%'],
      fontSize: 12,
      fontWeight: 'bold',
      color: '#464646'
    },
    detail: {
      fontSize: 16,
      fontWeight: 'bold',
      offsetCenter: [0, '-35%'],
      valueAnimation: true,
      formatter: function (value: number) {
        return Math.round(value) + '%';
      },
      color: 'auto'
    },
    data: [{
      value: 0,
      name: 'CPU使用率'
    }]
  }]
}));

// 计算资源使用情况
const resourceStats = computed(() => {
  if (!props.systemData || !props.systemData.available) {
    return {
      cpuUsage: 0,
      memoryUsage: 0,
      diskUsage: 0,
      memoryTotal: 0,
      memoryUsed: 0,
      diskTotal: 0,
      diskUsed: 0,
      available: false
    };
  }

  return {
    cpuUsage: props.systemData.cpu?.percent || 0,
    memoryUsage: props.systemData.memory?.percent || 0,
    diskUsage: props.systemData.disk?.percent || 0,
    memoryTotal: props.systemData.memory?.total || 0,
    memoryUsed: props.systemData.memory?.used || 0,
    diskTotal: props.systemData.disk?.total || 0,
    diskUsed: props.systemData.disk?.used || 0,
    available: props.systemData.available
  };
});

// 获取使用率状态
function getUsageStatus(usage: number) {
  if (usage <= 60) return { type: 'success', color: '#52c41a', text: '正常' };
  if (usage <= 80) return { type: 'warning', color: '#faad14', text: '注意' };
  return { type: 'error', color: '#ff4d4f', text: '警告' };
}

// 格式化字节大小
function formatBytes(bytes: number) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 监听数据变化，更新图表
watch(() => props.systemData, (newData) => {
  if (newData?.available) {
    const stats = resourceStats.value;

    // 确保数据有效后再更新图表
    if (stats.available) {
      try {
        // 验证数据有效性
        const isValidData = typeof stats.cpuUsage === 'number' &&
                           typeof stats.memoryUsage === 'number' &&
                           typeof stats.diskUsage === 'number' &&
                           stats.cpuUsage >= 0 && stats.memoryUsage >= 0 && stats.diskUsage >= 0;

        if (isValidData) {
          // 更新环形图
          updateResourceRing(prev => ({
            ...prev,
            series: [{
              ...prev.series[0],
              data: [
                { value: stats.cpuUsage, name: 'CPU', itemStyle: { color: '#1890ff' } },
                { value: stats.memoryUsage, name: '内存', itemStyle: { color: '#52c41a' } },
                { value: stats.diskUsage, name: '磁盘', itemStyle: { color: '#faad14' } }
              ]
            }]
          }));

          // 更新CPU仪表盘
          updateCpuGauge(prev => ({
            ...prev,
            series: [{
              ...prev.series[0],
              data: [{
                value: stats.cpuUsage,
                name: 'CPU使用率'
              }]
            }]
          }));
        }
      } catch (error) {
        console.error('Error updating resource charts:', error);
      }
    }
  }
});

function goToSystemDetail() {
  router.push('/monitor/system');
}
</script>

<template>
  <NCard class="enhanced-resource-overview-card">
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <Icon icon="mdi:server" class="text-2xl text-blue-500" />
          <span class="text-lg font-semibold">资源监控</span>
        </div>
        <NButton text @click="goToSystemDetail">
          <template #icon>
            <Icon icon="mdi:arrow-right" />
          </template>
          查看详情
        </NButton>
      </div>
    </template>

    <div v-if="!resourceStats.available" class="text-center py-12">
      <Icon icon="mdi:server-off" class="text-6xl text-gray-300 mb-4" />
      <NTag type="warning" size="large">系统资源监控不可用</NTag>
      <p class="text-sm text-gray-500 mt-2">请安装 psutil 库以启用资源监控</p>
    </div>

    <div v-else>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- CPU使用率仪表盘 -->
        <div class="chart-container">
          <div ref="cpuGaugeRef" class="cpu-gauge"></div>
        </div>

        <!-- 资源使用率环形图 -->
        <div class="chart-container">
          <div ref="resourceRingRef" class="resource-ring-chart"></div>
        </div>
      </div>

      <!-- 资源详细信息 -->
      <div class="mt-6">
        <NGrid :cols="1" :md-cols="3" :x-gap="16" :y-gap="16">
          <!-- CPU信息 -->
          <NGridItem>
            <div class="resource-card cpu">
              <div class="resource-header">
                <Icon icon="mdi:cpu-64-bit" class="text-2xl" />
                <span class="resource-title">CPU</span>
              </div>
              <div class="resource-content">
                <div class="resource-value">{{ resourceStats.cpuUsage.toFixed(1) }}%</div>
                <NProgress
                  :percentage="resourceStats.cpuUsage"
                  :color="getUsageStatus(resourceStats.cpuUsage).color"
                  :show-indicator="false"
                  :height="8"
                  class="mt-2"
                />
                <NTag :type="getUsageStatus(resourceStats.cpuUsage).type" size="small" class="mt-2">
                  {{ getUsageStatus(resourceStats.cpuUsage).text }}
                </NTag>
              </div>
            </div>
          </NGridItem>

          <!-- 内存信息 -->
          <NGridItem>
            <div class="resource-card memory">
              <div class="resource-header">
                <Icon icon="mdi:memory" class="text-2xl" />
                <span class="resource-title">内存</span>
              </div>
              <div class="resource-content">
                <div class="resource-value">{{ resourceStats.memoryUsage.toFixed(1) }}%</div>
                <div class="resource-detail">
                  {{ formatBytes(resourceStats.memoryUsed) }} / {{ formatBytes(resourceStats.memoryTotal) }}
                </div>
                <NProgress
                  :percentage="resourceStats.memoryUsage"
                  :color="getUsageStatus(resourceStats.memoryUsage).color"
                  :show-indicator="false"
                  :height="8"
                  class="mt-2"
                />
                <NTag :type="getUsageStatus(resourceStats.memoryUsage).type" size="small" class="mt-2">
                  {{ getUsageStatus(resourceStats.memoryUsage).text }}
                </NTag>
              </div>
            </div>
          </NGridItem>

          <!-- 磁盘信息 -->
          <NGridItem>
            <div class="resource-card disk">
              <div class="resource-header">
                <Icon icon="mdi:harddisk" class="text-2xl" />
                <span class="resource-title">磁盘</span>
              </div>
              <div class="resource-content">
                <div class="resource-value">{{ resourceStats.diskUsage.toFixed(1) }}%</div>
                <div class="resource-detail">
                  {{ formatBytes(resourceStats.diskUsed) }} / {{ formatBytes(resourceStats.diskTotal) }}
                </div>
                <NProgress
                  :percentage="resourceStats.diskUsage"
                  :color="getUsageStatus(resourceStats.diskUsage).color"
                  :show-indicator="false"
                  :height="8"
                  class="mt-2"
                />
                <NTag :type="getUsageStatus(resourceStats.diskUsage).type" size="small" class="mt-2">
                  {{ getUsageStatus(resourceStats.diskUsage).text }}
                </NTag>
              </div>
            </div>
          </NGridItem>
        </NGrid>
      </div>

      <!-- 资源状态总览 -->
      <div class="mt-6 p-4 bg-gray-50 rounded-lg">
        <div class="flex justify-between items-center mb-3">
          <span class="text-sm font-medium text-gray-700">资源状态评估</span>
          <div class="flex gap-2">
            <NTag :type="getUsageStatus(resourceStats.cpuUsage).type" size="small">
              CPU: {{ getUsageStatus(resourceStats.cpuUsage).text }}
            </NTag>
            <NTag :type="getUsageStatus(resourceStats.memoryUsage).type" size="small">
              内存: {{ getUsageStatus(resourceStats.memoryUsage).text }}
            </NTag>
            <NTag :type="getUsageStatus(resourceStats.diskUsage).type" size="small">
              磁盘: {{ getUsageStatus(resourceStats.diskUsage).text }}
            </NTag>
          </div>
        </div>

        <div class="grid grid-cols-3 gap-4">
          <div>
            <div class="text-xs text-gray-500 mb-1">CPU负载</div>
            <NProgress
              :percentage="resourceStats.cpuUsage"
              :color="getUsageStatus(resourceStats.cpuUsage).color"
              :show-indicator="false"
              :height="6"
            />
          </div>
          <div>
            <div class="text-xs text-gray-500 mb-1">内存占用</div>
            <NProgress
              :percentage="resourceStats.memoryUsage"
              :color="getUsageStatus(resourceStats.memoryUsage).color"
              :show-indicator="false"
              :height="6"
            />
          </div>
          <div>
            <div class="text-xs text-gray-500 mb-1">磁盘占用</div>
            <NProgress
              :percentage="resourceStats.diskUsage"
              :color="getUsageStatus(resourceStats.diskUsage).color"
              :show-indicator="false"
              :height="6"
            />
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="mt-6 pt-4 border-t border-gray-200">
        <NSpace justify="center">
          <NButton @click="goToSystemDetail" type="primary" ghost>
            <template #icon>
              <Icon icon="mdi:monitor-dashboard" />
            </template>
            查看系统详情
          </NButton>
        </NSpace>
      </div>
    </div>
  </NCard>
</template>

<style scoped>
.enhanced-resource-overview-card {
  height: 100%;
  transition: all 0.3s ease;
}

.enhanced-resource-overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.chart-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.cpu-gauge,
.resource-ring-chart {
  width: 100%;
  height: 200px;
}

.resource-card {
  padding: 20px;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  background: white;
  border: 1px solid #e2e8f0;
}

.resource-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.resource-card.cpu {
  border-left: 4px solid #1890ff;
}

.resource-card.memory {
  border-left: 4px solid #52c41a;
}

.resource-card.disk {
  border-left: 4px solid #faad14;
}

.resource-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.resource-title {
  font-weight: 600;
  color: #1e293b;
}

.resource-content {
  text-align: center;
}

.resource-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1e293b;
  line-height: 1;
}

.resource-detail {
  font-size: 0.75rem;
  color: #64748b;
  margin-top: 4px;
}

@media (max-width: 1024px) {
  .grid {
    grid-template-columns: 1fr;
  }

  .cpu-gauge,
  .resource-ring-chart {
    height: 150px;
  }
}

@media (max-width: 768px) {
  .resource-card {
    padding: 16px;
  }

  .resource-value {
    font-size: 1.25rem;
  }
}
</style>
