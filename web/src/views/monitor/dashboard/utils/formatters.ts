/**
 * 监控数据格式化工具函数
 */

/**
 * 标准化百分比值
 * 处理后端返回的不同格式的百分比数据
 * @param value 原始值
 * @returns 标准化后的百分比值 (0-100)
 */
export function normalizePercentage(value: number | undefined | null): number {
  if (value == null) return 0;
  
  // 如果值小于等于1，认为是小数格式(0-1)，需要乘以100
  // 否则认为已经是百分比格式(0-100)
  const percentage = value <= 1 ? value * 100 : value;
  
  // 确保不超过100%，不小于0%
  return Math.max(0, Math.min(percentage, 100));
}

/**
 * 格式化百分比显示
 * @param value 百分比值 (0-100)
 * @param decimals 小数位数，默认1位
 * @returns 格式化后的百分比字符串
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`;
}

/**
 * 格式化响应时间
 * @param timeInSeconds 响应时间（秒）
 * @returns 格式化后的时间字符串
 */
export function formatResponseTime(timeInSeconds: number): string {
  if (timeInSeconds < 1) {
    return `${(timeInSeconds * 1000).toFixed(0)}ms`;
  }
  return `${timeInSeconds.toFixed(2)}s`;
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小字符串
 */
export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

/**
 * 格式化持续时间
 * @param seconds 秒数
 * @returns 格式化后的持续时间字符串
 */
export function formatDuration(seconds: number): string {
  if (seconds < 60) return `${seconds.toFixed(1)}秒`;
  if (seconds < 3600) return `${(seconds / 60).toFixed(1)}分钟`;
  return `${(seconds / 3600).toFixed(1)}小时`;
}

/**
 * 获取状态颜色
 * @param status 状态字符串
 * @returns 对应的颜色值
 */
export function getStatusColor(status: string): string {
  switch (status) {
    case 'healthy':
    case 'success':
    case 'completed':
      return '#52c41a';
    case 'warning':
    case 'pending':
      return '#faad14';
    case 'error':
    case 'failed':
    case 'unhealthy':
      return '#ff4d4f';
    case 'running':
    case 'info':
      return '#1890ff';
    default:
      return '#d9d9d9';
  }
}

/**
 * 获取成功率状态
 * @param rate 成功率 (0-100)
 * @returns 状态类型
 */
export function getSuccessRateStatus(rate: number): 'success' | 'warning' | 'error' {
  if (rate >= 95) return 'success';
  if (rate >= 85) return 'warning';
  return 'error';
}

/**
 * 获取使用率状态
 * @param usage 使用率 (0-100)
 * @returns 状态信息
 */
export function getUsageStatus(usage: number): { type: 'success' | 'warning' | 'error'; color: string } {
  if (usage <= 60) return { type: 'success', color: '#52c41a' };
  if (usage <= 80) return { type: 'warning', color: '#faad14' };
  return { type: 'error', color: '#ff4d4f' };
}

/**
 * 获取响应时间状态
 * @param timeInSeconds 响应时间（秒）
 * @returns 状态信息
 */
export function getResponseTimeStatus(timeInSeconds: number): { 
  type: 'success' | 'warning' | 'error'; 
  text: string 
} {
  if (timeInSeconds <= 1) return { type: 'success', text: '优秀' };
  if (timeInSeconds <= 3) return { type: 'warning', text: '良好' };
  return { type: 'error', text: '较慢' };
}

/**
 * 获取错误率状态
 * @param rate 错误率 (0-100)
 * @returns 状态信息
 */
export function getErrorRateStatus(rate: number): { 
  type: 'success' | 'warning' | 'error'; 
  text: string 
} {
  if (rate <= 1) return { type: 'success', text: '正常' };
  if (rate <= 5) return { type: 'warning', text: '注意' };
  return { type: 'error', text: '异常' };
}
