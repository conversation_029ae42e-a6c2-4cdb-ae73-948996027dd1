<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NCard, NGrid, NGridItem, NStatistic, NSpin, NAlert, NSelect, NSpace, NTag } from 'naive-ui';
import { fetchErrorAnalysis } from '@/service/api';

// 错误分析数据
const loading = ref(true);
const error = ref<string | null>(null);
const errorData = ref<Api.Monitor.ErrorAnalysisData | null>(null);

// 分析时间范围选项
const timeRangeOptions = [
  { label: '最近1小时', value: 1 },
  { label: '最近6小时', value: 6 },
  { label: '最近24小时', value: 24 },
  { label: '最近3天', value: 72 },
  { label: '最近7天', value: 168 }
];

const selectedTimeRange = ref(24);

onMounted(async () => {
  await loadErrorData();
});

async function loadErrorData() {
  try {
    loading.value = true;
    error.value = null;

    // 调用监控API获取错误分析数据
    const { data } = await fetchErrorAnalysis(selectedTimeRange.value);
    errorData.value = data;
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载错误分析数据失败';
    console.error('加载错误分析数据失败:', err);
  } finally {
    loading.value = false;
  }
}

// 时间范围变化处理
async function handleTimeRangeChange() {
  await loadErrorData();
}

// 获取错误级别颜色
function getErrorLevelColor(errorCode: string) {
  if (errorCode.startsWith('4')) return 'warning';
  if (errorCode.startsWith('5')) return 'error';
  return 'default';
}

// 外部数据更新方法
function updateData(newData: Api.Monitor.ErrorAnalysisData) {
  errorData.value = newData;
  error.value = null;
}

// 暴露方法供父组件调用
defineExpose({
  loadErrorData,
  updateData
});
</script>

<template>
  <NCard title="错误分析" class="monitor-card">
    <template #header-extra>
      <NSpace>
        <NSelect
          v-model:value="selectedTimeRange"
          :options="timeRangeOptions"
          size="small"
          style="width: 120px"
          @update:value="handleTimeRangeChange"
        />
      </NSpace>
    </template>

    <NSpin :show="loading">
      <div v-if="error" class="mb-4">
        <NAlert type="error" :title="error" />
      </div>

      <div v-if="errorData" class="space-y-6">
        <!-- 错误概览统计 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">错误概览</h3>
          <NGrid :cols="4" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NStatistic
                label="总错误数"
                :value="errorData.error_summary.total_errors"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="总请求数"
                :value="errorData.error_summary.total_requests"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="API错误率"
                :value="`${(errorData.error_summary.error_rate * 100).toFixed(2)}%`"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="任务失败率"
                :value="`${(errorData.failed_tasks.failure_rate * 100).toFixed(2)}%`"
              />
            </NGridItem>
          </NGrid>
        </div>

        <!-- 错误类型分布 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">错误类型分布</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div v-for="errorType in errorData.error_summary.top_errors" :key="errorType.error_code" 
                 class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center gap-2">
                <NTag :type="getErrorLevelColor(errorType.error_code)" size="small">
                  {{ errorType.error_code }}
                </NTag>
                <span class="font-medium">{{ errorType.error_type }}</span>
              </div>
              <div class="text-right">
                <div class="font-semibold">{{ errorType.count }}</div>
                <div class="text-sm text-gray-500">{{ errorType.percentage.toFixed(1) }}%</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 错误端点统计 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">错误端点统计</h3>
          <div class="space-y-2">
            <div v-for="endpoint in errorData.error_summary.top_error_endpoints.slice(0, 5)" 
                 :key="endpoint.endpoint"
                 class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div class="flex-1">
                <div class="font-medium text-sm">{{ endpoint.endpoint }}</div>
                <div class="flex gap-1 mt-1">
                  <NTag v-for="method in endpoint.methods" :key="method" size="tiny" type="info">
                    {{ method }}
                  </NTag>
                  <NTag v-for="code in endpoint.error_codes" :key="code" size="tiny" 
                        :type="getErrorLevelColor(code)">
                    {{ code }}
                  </NTag>
                </div>
              </div>
              <div class="text-right">
                <div class="font-semibold">{{ endpoint.count }}</div>
                <div class="text-sm text-gray-500">错误次数</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 任务失败统计 -->
        <div v-if="errorData.failed_tasks.total > 0">
          <h3 class="text-lg font-semibold mb-4">任务失败统计</h3>
          <NGrid :cols="3" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NStatistic
                label="失败任务数"
                :value="errorData.failed_tasks.total"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="总任务数"
                :value="errorData.failed_tasks.total_tasks"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="失败率"
                :value="`${(errorData.failed_tasks.failure_rate * 100).toFixed(2)}%`"
              />
            </NGridItem>
          </NGrid>
          
          <div v-if="errorData.failed_tasks.by_type.length > 0" class="mt-4">
            <h4 class="font-medium mb-2">按类型分布</h4>
            <div class="flex gap-2">
              <NTag v-for="taskType in errorData.failed_tasks.by_type" :key="taskType.type" type="error">
                {{ taskType.type }}: {{ taskType.count }}
              </NTag>
            </div>
          </div>
        </div>

        <!-- 分析时间范围提示 -->
        <div class="text-sm text-gray-500 text-center">
          分析时间范围: 最近 {{ errorData.analysis_period_hours }} 小时
        </div>
      </div>
    </NSpin>
  </NCard>
</template>

<style scoped>
.monitor-card {
  height: 100%;
}
</style>
