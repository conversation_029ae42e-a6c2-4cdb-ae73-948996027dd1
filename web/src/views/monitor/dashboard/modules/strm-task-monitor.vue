<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { NCard, NGrid, NGridItem, NStatistic, NSpin, NAlert, NSelect, NSpace, NTag, NProgress } from 'naive-ui';
import { fetchStrmTaskStats } from '@/service/api';

// STRM任务统计数据
const loading = ref(true);
const error = ref<string | null>(null);
const taskStats = ref<Api.Monitor.StrmTaskStatsData | null>(null);

// 分析时间范围选项
const timeRangeOptions = [
  { label: '最近1小时', value: 1 },
  { label: '最近6小时', value: 6 },
  { label: '最近24小时', value: 24 },
  { label: '最近3天', value: 72 },
  { label: '最近7天', value: 168 }
];

const selectedTimeRange = ref(24);

onMounted(async () => {
  await loadTaskStats();
});

async function loadTaskStats() {
  try {
    loading.value = true;
    error.value = null;

    // 调用监控API获取STRM任务统计数据
    const { data } = await fetchStrmTaskStats(selectedTimeRange.value);
    taskStats.value = data;
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载STRM任务统计失败';
    console.error('加载STRM任务统计失败:', err);
  } finally {
    loading.value = false;
  }
}

// 时间范围变化处理
async function handleTimeRangeChange() {
  await loadTaskStats();
}

// 计算状态分布百分比
const statusPercentages = computed(() => {
  if (!taskStats.value) return {};

  const { status_distribution } = taskStats.value;
  const total = Object.values(status_distribution).reduce((sum, count) => sum + count, 0);

  if (total === 0) return {};

  return {
    pending: (status_distribution.pending / total) * 100,
    running: (status_distribution.running / total) * 100,
    completed: (status_distribution.completed / total) * 100,
    failed: (status_distribution.failed / total) * 100
  };
});

// 获取状态标签类型
function getStatusTagType(status: string) {
  switch (status) {
    case 'completed': return 'success';
    case 'running': return 'info';
    case 'pending': return 'warning';
    case 'failed': return 'error';
    default: return 'default';
  }
}

// 获取状态中文名称
function getStatusLabel(status: string) {
  switch (status) {
    case 'completed': return '已完成';
    case 'running': return '运行中';
    case 'pending': return '等待中';
    case 'failed': return '失败';
    default: return status;
  }
}

// 格式化时间
function formatDuration(seconds: number) {
  if (seconds < 60) return `${seconds.toFixed(1)}秒`;
  if (seconds < 3600) return `${(seconds / 60).toFixed(1)}分钟`;
  return `${(seconds / 3600).toFixed(1)}小时`;
}

// 外部数据更新方法
function updateData(newData: Api.Monitor.StrmTaskStatsData) {
  taskStats.value = newData;
  error.value = null;
}

// 暴露方法供父组件调用
defineExpose({
  loadTaskStats,
  updateData
});
</script>

<template>
  <NCard title="STRM任务监控" class="monitor-card">
    <template #header-extra>
      <NSpace>
        <NSelect
          v-model:value="selectedTimeRange"
          :options="timeRangeOptions"
          size="small"
          style="width: 120px"
          @update:value="handleTimeRangeChange"
        />
      </NSpace>
    </template>

    <NSpin :show="loading">
      <div v-if="error" class="mb-4">
        <NAlert type="error" :title="error" />
      </div>

      <div v-if="taskStats" class="space-y-6">
        <!-- 任务状态分布 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">任务状态分布</h3>
          <NGrid :cols="4" :x-gap="16" :y-gap="16">
            <NGridItem v-for="(count, status) in taskStats.status_distribution" :key="status">
              <div class="text-center">
                <NTag :type="getStatusTagType(status)" size="large" class="mb-2">
                  {{ getStatusLabel(status) }}
                </NTag>
                <div class="text-2xl font-bold">{{ count }}</div>
                <div class="text-sm text-gray-500">
                  {{ statusPercentages[status]?.toFixed(1) || 0 }}%
                </div>
                <NProgress
                  :percentage="statusPercentages[status] || 0"
                  :color="getStatusTagType(status) === 'success' ? '#18a058' :
                         getStatusTagType(status) === 'error' ? '#d03050' :
                         getStatusTagType(status) === 'warning' ? '#f0a020' : '#2080f0'"
                  :show-indicator="false"
                  class="mt-2"
                />
              </div>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 处理性能统计 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">处理性能统计</h3>
          <NGrid :cols="4" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NStatistic
                label="平均处理时间"
                :value="formatDuration(taskStats.processing_stats.avg_duration)"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="最快处理时间"
                :value="formatDuration(taskStats.processing_stats.min_duration)"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="最慢处理时间"
                :value="formatDuration(taskStats.processing_stats.max_duration)"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="中位数处理时间"
                :value="formatDuration(taskStats.processing_stats.median_duration)"
              />
            </NGridItem>
          </NGrid>
        </div>

        <!-- 队列状态 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">队列状态</h3>
          <NGrid :cols="3" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NStatistic
                label="当前运行"
                :value="taskStats.queue_status.current_running"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="等待队列"
                :value="taskStats.queue_status.current_pending"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="队列总长度"
                :value="taskStats.queue_status.queue_length"
              />
            </NGridItem>
          </NGrid>
        </div>

        <!-- 性能洞察 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">性能洞察</h3>
          <NGrid :cols="2" :x-gap="16" :y-gap="16">
            <NGridItem>
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium mb-2">总体统计</h4>
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span>成功率:</span>
                    <span class="font-semibold">{{ ((taskStats.performance_insights.success_rate <= 1 ? taskStats.performance_insights.success_rate * 100 : taskStats.performance_insights.success_rate)).toFixed(1) }}%</span>
                  </div>
                  <div class="flex justify-between">
                    <span>已完成:</span>
                    <span class="font-semibold">{{ taskStats.performance_insights.total_completed }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span>失败数:</span>
                    <span class="font-semibold">{{ taskStats.performance_insights.total_failed }}</span>
                  </div>
                </div>
              </div>
            </NGridItem>

            <NGridItem>
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="font-medium mb-2">极值任务</h4>
                <div class="space-y-2">
                  <div v-if="taskStats.performance_insights.fastest_task">
                    <div class="text-sm text-gray-600">最快任务:</div>
                    <div class="font-medium">{{ taskStats.performance_insights.fastest_task.name }}</div>
                    <div class="text-sm">{{ formatDuration(taskStats.performance_insights.fastest_task.duration) }}</div>
                  </div>
                  <div v-if="taskStats.performance_insights.slowest_task">
                    <div class="text-sm text-gray-600">最慢任务:</div>
                    <div class="font-medium">{{ taskStats.performance_insights.slowest_task.name }}</div>
                    <div class="text-sm">{{ formatDuration(taskStats.performance_insights.slowest_task.duration) }}</div>
                  </div>
                </div>
              </div>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 最近失败任务 -->
        <div v-if="taskStats.recent_failures.length > 0">
          <h3 class="text-lg font-semibold mb-4">最近失败任务</h3>
          <div class="space-y-2">
            <div v-for="failure in taskStats.recent_failures" :key="failure.id"
                 class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <div class="flex-1">
                <div class="font-medium">{{ failure.name }}</div>
                <div class="text-sm text-gray-600">{{ failure.error_message }}</div>
                <div class="text-xs text-gray-500">
                  {{ new Date(failure.create_time).toLocaleString() }} | 文件数: {{ failure.file_count }}
                </div>
              </div>
              <NTag type="error" size="small">失败</NTag>
            </div>
          </div>
        </div>

        <!-- 分析时间范围提示 -->
        <div class="text-sm text-gray-500 text-center">
          分析时间范围: 最近 {{ taskStats.analysis_period_hours }} 小时 |
          总任务数: {{ taskStats.total_tasks_analyzed }}
        </div>
      </div>
    </NSpin>
  </NCard>
</template>

<style scoped>
.monitor-card {
  height: 100%;
}
</style>
