<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { NCard, NGrid, NGridItem, NStatistic, NSpin, NAlert } from 'naive-ui';
import { fetchBusinessStats } from '@/service/api';
import { useEcharts } from '@/hooks/common/echarts';

// 业务统计数据
const loading = ref(true);
const error = ref<string | null>(null);
const businessData = ref<Api.Monitor.BusinessStatsData | null>(null);

// STRM任务状态分布饼图
const { domRef: taskStatusChartRef, updateOptions: updateTaskStatusChart } = useEcharts(() => ({
  title: {
    text: 'STRM任务状态分布',
    left: 'center',
    fontSize: 14
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    bottom: '5%',
    left: 'center'
  },
  series: [{
    name: '任务状态',
    type: 'pie',
    radius: ['40%', '70%'],
    center: ['50%', '45%'],
    data: [] as Array<{ name: string; value: number; itemStyle: { color: string } }>,
    itemStyle: {
      borderRadius: 8,
      borderColor: '#fff',
      borderWidth: 2
    },
    label: {
      show: false
    },
    emphasis: {
      label: {
        show: true,
        fontSize: '12',
        fontWeight: 'bold'
      }
    }
  }]
}));

// 用户活动趋势图
const { domRef: userActivityChartRef, updateOptions: updateUserActivityChart } = useEcharts(() => ({
  title: {
    text: '用户活动趋势',
    left: 'center',
    fontSize: 14
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['活跃用户', '登录次数'],
    bottom: '5%'
  },
  xAxis: {
    type: 'category',
    data: [] as string[]
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '活跃用户',
      type: 'line',
      smooth: true,
      data: [] as number[],
      lineStyle: { color: '#5da8ff' },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(93, 168, 255, 0.3)' },
            { offset: 1, color: 'rgba(93, 168, 255, 0.1)' }
          ]
        }
      }
    },
    {
      name: '登录次数',
      type: 'bar',
      data: [] as number[],
      itemStyle: { color: '#26deca' }
    }
  ]
}));

// API请求量仪表盘
const { domRef: apiRequestGaugeRef, updateOptions: updateApiRequestGauge } = useEcharts(() => ({
  title: {
    text: 'API请求量',
    left: 'center',
    fontSize: 14
  },
  series: [{
    name: 'API请求',
    type: 'gauge',
    center: ['50%', '60%'],
    startAngle: 200,
    endAngle: -20,
    min: 0,
    max: 1000,
    splitNumber: 5,
    itemStyle: {
      color: '#58D9F9',
      shadowColor: 'rgba(0,138,255,0.45)',
      shadowBlur: 10,
      shadowOffsetX: 2,
      shadowOffsetY: 2
    },
    progress: {
      show: true,
      roundCap: true,
      width: 18
    },
    pointer: {
      icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
      length: '75%',
      width: 16,
      offsetCenter: [0, '5%']
    },
    axisLine: {
      roundCap: true,
      lineStyle: {
        width: 18
      }
    },
    axisTick: {
      splitNumber: 2,
      lineStyle: {
        width: 2,
        color: '#999'
      }
    },
    splitLine: {
      length: 12,
      lineStyle: {
        width: 3,
        color: '#999'
      }
    },
    axisLabel: {
      distance: 30,
      color: '#999',
      fontSize: 12
    },
    title: {
      show: false
    },
    detail: {
      backgroundColor: '#fff',
      borderColor: '#999',
      borderWidth: 2,
      width: '60%',
      lineHeight: 40,
      height: 40,
      borderRadius: 8,
      offsetCenter: [0, '35%'],
      valueAnimation: true,
      formatter: function (value: number) {
        return '{value|' + value.toFixed(0) + '}{unit|次}';
      },
      rich: {
        value: {
          fontSize: 20,
          fontWeight: 'bolder',
          color: '#777'
        },
        unit: {
          fontSize: 12,
          color: '#999',
          padding: [0, 0, -20, 10]
        }
      }
    },
    data: [{ value: 0 }]
  }]
}));

onMounted(async () => {
  await loadBusinessData();
});

async function loadBusinessData() {
  try {
    loading.value = true;
    error.value = null;

    // 调用监控API获取业务统计数据
    const { data } = await fetchBusinessStats();
    businessData.value = data;

    // 更新图表数据
    updateCharts();
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载业务数据失败';
    console.error('加载业务统计数据失败:', err);
  } finally {
    loading.value = false;
  }
}

// 监听数据变化，更新图表
watch(businessData, () => {
  updateCharts();
}, { deep: true });

function updateCharts() {
  if (!businessData.value) return;

  // 更新STRM任务状态分布饼图
  const taskStatusData = [
    {
      name: '已完成',
      value: businessData.value.strm_tasks.completed,
      itemStyle: { color: '#52c41a' }
    },
    {
      name: '运行中',
      value: businessData.value.strm_tasks.running,
      itemStyle: { color: '#1890ff' }
    },
    {
      name: '失败',
      value: businessData.value.strm_tasks.failed,
      itemStyle: { color: '#ff4d4f' }
    },
    {
      name: '等待中',
      value: businessData.value.strm_tasks.total - businessData.value.strm_tasks.completed - businessData.value.strm_tasks.running - businessData.value.strm_tasks.failed,
      itemStyle: { color: '#faad14' }
    }
  ].filter(item => item.value > 0);

  updateTaskStatusChart(prev => ({
    ...prev,
    series: [{ ...prev.series[0], data: taskStatusData }]
  }));

  // 更新用户活动趋势图（模拟7天数据）
  const days = Array.from({ length: 7 }, (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() - (6 - i));
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
  });

  const activeUsersData = Array.from({ length: 7 }, () =>
    Math.floor(Math.random() * 50 + businessData.value!.user_activity.active_today)
  );

  const loginCountData = Array.from({ length: 7 }, () =>
    Math.floor(Math.random() * 100 + businessData.value!.user_activity.login_count_today)
  );

  updateUserActivityChart(prev => ({
    ...prev,
    xAxis: { ...prev.xAxis, data: days },
    series: [
      { ...prev.series[0], data: activeUsersData },
      { ...prev.series[1], data: loginCountData }
    ]
  }));

  // 更新API请求量仪表盘
  updateApiRequestGauge(prev => ({
    ...prev,
    series: [{
      ...prev.series[0],
      data: [{ value: businessData.value!.api_requests.total_requests_today }]
    }]
  }));
}

// 外部数据更新方法
function updateData(newData: Api.Monitor.BusinessStatsData) {
  businessData.value = newData;
  error.value = null;
}

// 暴露方法供父组件调用
defineExpose({
  loadBusinessData,
  updateData
});

function formatDuration(seconds: number) {
  if (seconds < 60) return `${seconds.toFixed(1)}秒`;
  if (seconds < 3600) return `${(seconds / 60).toFixed(1)}分钟`;
  return `${(seconds / 3600).toFixed(1)}小时`;
}
</script>

<template>
  <NCard title="业务统计" class="h-full">
    <NSpin :show="loading">
      <div v-if="error" class="mb-4">
        <NAlert type="error" :title="error" closable @close="error = null" />
      </div>

      <div v-if="businessData" class="space-y-6">
        <!-- STRM任务统计 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">STRM任务统计</h3>
          <NGrid :cols="4" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NStatistic label="总任务数" :value="businessData.strm_tasks.total" />
            </NGridItem>
            <NGridItem>
              <NStatistic label="已完成" :value="businessData.strm_tasks.completed" />
            </NGridItem>
            <NGridItem>
              <NStatistic label="运行中" :value="businessData.strm_tasks.running" />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="成功率"
                :value="`${((businessData.strm_tasks.success_rate <= 1 ? businessData.strm_tasks.success_rate * 100 : businessData.strm_tasks.success_rate)).toFixed(1)}%`"
              />
            </NGridItem>
          </NGrid>

          <div class="mt-4">
            <NGrid :cols="2" :x-gap="16">
              <NGridItem>
                <NStatistic label="失败任务" :value="businessData.strm_tasks.failed" />
              </NGridItem>
              <NGridItem>
                <NStatistic
                  label="平均处理时间"
                  :value="formatDuration(businessData.strm_tasks.avg_processing_time)"
                />
              </NGridItem>
            </NGrid>
          </div>
        </div>

        <!-- STRM任务状态分布图 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">任务状态分布</h3>
          <NCard size="small" class="chart-card">
            <div ref="taskStatusChartRef" class="chart-container"></div>
          </NCard>
        </div>

        <!-- 用户活动统计 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">用户活动统计</h3>
          <NGrid :cols="4" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NStatistic label="总用户数" :value="businessData.user_activity.total_users" />
            </NGridItem>
            <NGridItem>
              <NStatistic label="今日活跃" :value="businessData.user_activity.active_today" />
            </NGridItem>
            <NGridItem>
              <NStatistic label="今日登录" :value="businessData.user_activity.login_count_today" />
            </NGridItem>
            <NGridItem>
              <NStatistic label="本周活跃" :value="businessData.user_activity.active_users_week" />
            </NGridItem>
          </NGrid>
        </div>

        <!-- 用户活动趋势图 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">用户活动趋势</h3>
          <NCard size="small" class="chart-card">
            <div ref="userActivityChartRef" class="chart-container"></div>
          </NCard>
        </div>

        <!-- API请求统计 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">API请求统计</h3>
          <NGrid :cols="2" :x-gap="16" :y-gap="16">
            <!-- 统计数据 -->
            <NGridItem>
              <NGrid :cols="1" :y-gap="16">
                <NGridItem>
                  <NGrid :cols="3" :x-gap="16">
                    <NGridItem>
                      <NStatistic label="今日请求总数" :value="businessData.api_requests.total_requests_today" />
                    </NGridItem>
                    <NGridItem>
                      <NStatistic
                        label="平均响应时间"
                        :value="`${businessData.api_requests.avg_response_time.toFixed(2)}s`"
                      />
                    </NGridItem>
                    <NGridItem>
                      <NStatistic label="错误请求数" :value="businessData.api_requests.error_count" />
                    </NGridItem>
                  </NGrid>
                </NGridItem>

                <!-- 热门端点 -->
                <NGridItem>
                  <div>
                    <h4 class="text-md font-medium mb-3">热门API端点</h4>
                    <div class="space-y-2">
                      <div
                        v-for="(endpoint, index) in businessData.api_requests.top_endpoints"
                        :key="index"
                        class="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                      >
                        <span class="text-sm font-mono">{{ endpoint.endpoint }}</span>
                        <span class="text-sm font-semibold text-blue-600">{{ endpoint.count }} 次</span>
                      </div>
                    </div>
                  </div>
                </NGridItem>
              </NGrid>
            </NGridItem>

            <!-- API请求量仪表盘 -->
            <NGridItem>
              <NCard size="small" class="chart-card">
                <div ref="apiRequestGaugeRef" class="chart-container"></div>
              </NCard>
            </NGridItem>
          </NGrid>
        </div>
      </div>

      <div v-else-if="!loading" class="text-center text-gray-500 py-8">
        暂无业务统计数据
      </div>
    </NSpin>
  </NCard>
</template>

<style scoped>
.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.h-full {
  height: 100%;
}

.chart-card {
  height: 300px;
}

.chart-container {
  width: 100%;
  height: 260px;
}
</style>
