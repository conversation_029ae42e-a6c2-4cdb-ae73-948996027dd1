<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { NCard, NGrid, NGridItem, NStatistic, NSpin, NAlert, NProgress, NSpace, NTag } from 'naive-ui';
import { Icon } from '@iconify/vue';
import { fetchSystemOverview } from '@/service/api';
import { useEcharts } from '@/hooks/common/echarts';

// 系统资源数据
const loading = ref(true);
const error = ref<string | null>(null);
const systemData = ref<Api.Monitor.SystemOverviewData | null>(null);

// 系统资源仪表盘图表
const { domRef: resourceGaugeRef, updateOptions: updateResourceGauge } = useEcharts(() => ({
  title: {
    text: '系统资源使用率',
    left: 'center',
    fontSize: 16,
    fontWeight: 'bold'
  },
  series: [
    {
      name: 'CPU',
      type: 'gauge',
      center: ['25%', '55%'],
      radius: '60%',
      min: 0,
      max: 100,
      startAngle: 225,
      endAngle: -45,
      splitNumber: 5,
      itemStyle: {
        color: '#FF6B6B',
        shadowColor: 'rgba(255, 107, 107, 0.45)',
        shadowBlur: 10
      },
      progress: {
        show: true,
        width: 12
      },
      pointer: {
        show: false
      },
      axisLine: {
        lineStyle: {
          width: 12
        }
      },
      axisTick: {
        distance: -20,
        splitNumber: 5,
        lineStyle: {
          width: 2,
          color: '#999'
        }
      },
      splitLine: {
        distance: -25,
        length: 8,
        lineStyle: {
          width: 3,
          color: '#999'
        }
      },
      axisLabel: {
        distance: -35,
        color: '#999',
        fontSize: 10
      },
      anchor: {
        show: false
      },
      title: {
        show: true,
        offsetCenter: [0, '70%'],
        textStyle: {
          fontSize: 12,
          color: '#333'
        }
      },
      detail: {
        valueAnimation: true,
        width: '60%',
        lineHeight: 20,
        borderRadius: 8,
        offsetCenter: [0, '35%'],
        fontSize: 14,
        fontWeight: 'bolder',
        formatter: '{value}%',
        color: 'inherit'
      },
      data: [{ value: 0, name: 'CPU' }]
    },
    {
      name: '内存',
      type: 'gauge',
      center: ['75%', '55%'],
      radius: '60%',
      min: 0,
      max: 100,
      startAngle: 225,
      endAngle: -45,
      splitNumber: 5,
      itemStyle: {
        color: '#4ECDC4',
        shadowColor: 'rgba(78, 205, 196, 0.45)',
        shadowBlur: 10
      },
      progress: {
        show: true,
        width: 12
      },
      pointer: {
        show: false
      },
      axisLine: {
        lineStyle: {
          width: 12
        }
      },
      axisTick: {
        distance: -20,
        splitNumber: 5,
        lineStyle: {
          width: 2,
          color: '#999'
        }
      },
      splitLine: {
        distance: -25,
        length: 8,
        lineStyle: {
          width: 3,
          color: '#999'
        }
      },
      axisLabel: {
        distance: -35,
        color: '#999',
        fontSize: 10
      },
      anchor: {
        show: false
      },
      title: {
        show: true,
        offsetCenter: [0, '70%'],
        textStyle: {
          fontSize: 12,
          color: '#333'
        }
      },
      detail: {
        valueAnimation: true,
        width: '60%',
        lineHeight: 20,
        borderRadius: 8,
        offsetCenter: [0, '35%'],
        fontSize: 14,
        fontWeight: 'bolder',
        formatter: '{value}%',
        color: 'inherit'
      },
      data: [{ value: 0, name: '内存' }]
    }
  ]
}));

// 磁盘使用率仪表盘
const { domRef: diskGaugeRef, updateOptions: updateDiskGauge } = useEcharts(() => ({
  title: {
    text: '磁盘使用率',
    left: 'center',
    textStyle: { fontSize: 14 }
  },
  series: [{
    name: '磁盘使用率',
    type: 'gauge',
    center: ['50%', '60%'],
    startAngle: 200,
    endAngle: -20,
    min: 0,
    max: 100,
    splitNumber: 5,
    itemStyle: {
      color: '#FFE66D',
      shadowColor: 'rgba(255, 230, 109, 0.45)',
      shadowBlur: 10,
      shadowOffsetX: 2,
      shadowOffsetY: 2
    },
    progress: {
      show: true,
      roundCap: true,
      width: 18
    },
    pointer: {
      length: '75%',
      width: 16,
      offsetCenter: [0, '5%']
    },
    axisLine: {
      roundCap: true,
      lineStyle: {
        width: 18
      }
    },
    axisTick: {
      splitNumber: 2,
      lineStyle: {
        width: 2,
        color: '#999'
      }
    },
    splitLine: {
      length: 12,
      lineStyle: {
        width: 3,
        color: '#999'
      }
    },
    axisLabel: {
      distance: 30,
      color: '#999',
      fontSize: 12,
      formatter: '{value}%'
    },
    title: {
      show: false
    },
    detail: {
      backgroundColor: '#fff',
      borderColor: '#999',
      borderWidth: 2,
      width: '60%',
      lineHeight: 40,
      height: 40,
      borderRadius: 8,
      offsetCenter: [0, '35%'],
      valueAnimation: true,
      formatter: function (value: number) {
        return '{value|' + value.toFixed(1) + '}{unit|%}';
      },
      rich: {
        value: {
          fontSize: 20,
          fontWeight: 'bolder',
          color: '#777'
        },
        unit: {
          fontSize: 12,
          color: '#999',
          padding: [0, 0, -20, 10]
        }
      }
    },
    data: [{ value: 0 }]
  }]
}));

onMounted(async () => {
  await loadSystemData();
});

async function loadSystemData() {
  try {
    loading.value = true;
    error.value = null;

    // 调用监控API获取系统资源数据
    const { data } = await fetchSystemOverview();
    systemData.value = data;

    // 更新图表数据
    updateCharts();
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载系统资源数据失败';
    console.error('加载系统资源数据失败:', err);
  } finally {
    loading.value = false;
  }
}

// 监听数据变化，更新图表
watch(systemData, () => {
  updateCharts();
}, { deep: true });

function updateCharts() {
  if (!systemData.value || systemData.value.error || !systemData.value.available) return;

  try {
    // 更新系统资源仪表盘
    const cpuPercent = systemData.value.cpu?.percent || 0;
    const memoryPercent = systemData.value.memory?.percent || 0;

    // 验证数据有效性
    if (typeof cpuPercent === 'number' && typeof memoryPercent === 'number' &&
        cpuPercent >= 0 && memoryPercent >= 0) {
      updateResourceGauge(prev => ({
        ...prev,
        series: [
          { ...prev.series[0], data: [{ value: cpuPercent, name: 'CPU' }] },
          { ...prev.series[1], data: [{ value: memoryPercent, name: '内存' }] }
        ]
      }));
    }

    // 更新磁盘使用率仪表盘
    const diskPercent = systemData.value.disk?.percent || 0;
    if (typeof diskPercent === 'number' && diskPercent >= 0) {
      updateDiskGauge(prev => ({
        ...prev,
        series: [{
          ...prev.series[0],
          data: [{ value: diskPercent }]
        }]
      }));
    }
  } catch (error) {
    console.error('Error updating system resource charts:', error);
  }
}

// 格式化字节大小
function formatBytes(bytes: number, decimals = 2) {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// 格式化运行时间
function formatUptime(seconds: number) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) {
    return `${days}天 ${hours}小时 ${minutes}分钟`;
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分钟`;
  } else {
    return `${minutes}分钟`;
  }
}

// 获取使用率颜色
function getUsageColor(percent: number) {
  if (percent < 50) return '#18a058';
  if (percent < 80) return '#f0a020';
  return '#d03050';
}

// 获取使用率状态
function getUsageStatus(percent: number) {
  if (percent < 50) return 'success';
  if (percent < 80) return 'warning';
  return 'error';
}

// 获取程序使用率颜色（相对宽松的阈值）
function getAppUsageColor(percent: number) {
  if (percent < 30) return '#18a058';  // 绿色 - 正常
  if (percent < 60) return '#f0a020';  // 橙色 - 注意
  return '#d03050';                    // 红色 - 警告
}

// 格式化日期时间
function formatDateTime(isoString: string) {
  const date = new Date(isoString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// 计算网络速度（简单估算，实际应该基于时间间隔）
const networkStats = computed(() => {
  if (!systemData.value?.network) return null;

  const { bytes_sent, bytes_recv, packets_sent, packets_recv } = systemData.value.network;

  return {
    totalBytes: bytes_sent + bytes_recv,
    totalPackets: packets_sent + packets_recv,
    sentFormatted: formatBytes(bytes_sent),
    recvFormatted: formatBytes(bytes_recv)
  };
});

// 外部数据更新方法
function updateData(newData: Api.Monitor.SystemOverviewData) {
  systemData.value = newData;
  error.value = null;
}

// 暴露方法供父组件调用
defineExpose({
  loadSystemData,
  updateData
});
</script>

<template>
  <NCard title="系统资源监控" class="monitor-card">
    <NSpin :show="loading">
      <div v-if="error" class="mb-4">
        <NAlert type="error" :title="error" />
      </div>

      <div v-if="systemData && !systemData.error" class="space-y-6">
        <!-- 系统资源仪表盘概览 -->
        <div>
          <h3 class="text-lg font-semibold mb-4">系统资源概览</h3>
          <NGrid :cols="2" :x-gap="16" :y-gap="16">
            <!-- 双仪表盘：CPU和内存 -->
            <NGridItem>
              <NCard size="small" class="chart-card">
                <div ref="resourceGaugeRef" class="chart-container"></div>
              </NCard>
            </NGridItem>

            <!-- 磁盘使用率仪表盘 -->
            <NGridItem>
              <NCard size="small" class="chart-card">
                <div ref="diskGaugeRef" class="chart-container"></div>
              </NCard>
            </NGridItem>
          </NGrid>
        </div>

        <!-- CPU监控详情 -->
        <div v-if="systemData.cpu">
          <h3 class="text-lg font-semibold mb-4">CPU使用详情</h3>
          <NGrid :cols="3" :x-gap="16" :y-gap="16">
            <NGridItem>
              <div class="text-center">
                <div class="text-2xl font-bold mb-2">{{ systemData.cpu.percent }}%</div>
                <NProgress
                  :percentage="systemData.cpu.percent"
                  :color="getUsageColor(systemData.cpu.percent)"
                  :show-indicator="false"
                  class="mb-2"
                />
                <div class="text-sm text-gray-600">CPU使用率</div>
              </div>
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="物理核心"
                :value="systemData.cpu.cores_physical"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="逻辑核心"
                :value="systemData.cpu.cores_logical"
              />
            </NGridItem>
          </NGrid>

          <div v-if="systemData.cpu.load_avg.length > 0" class="mt-4">
            <h4 class="font-medium mb-2">负载平均值</h4>
            <NSpace>
              <NTag v-for="(load, index) in systemData.cpu.load_avg" :key="index" type="info">
                {{ ['1分钟', '5分钟', '15分钟'][index] }}: {{ load }}
              </NTag>
            </NSpace>
          </div>
        </div>

        <!-- 内存监控 -->
        <div v-if="systemData.memory">
          <h3 class="text-lg font-semibold mb-4">内存使用情况</h3>
          <NGrid :cols="2" :x-gap="16" :y-gap="16">
            <NGridItem>
              <div class="text-center">
                <div class="text-2xl font-bold mb-2">{{ systemData.memory.percent }}%</div>
                <NProgress
                  :percentage="systemData.memory.percent"
                  :color="getUsageColor(systemData.memory.percent)"
                  :show-indicator="false"
                  class="mb-2"
                />
                <div class="text-sm text-gray-600">内存使用率</div>
              </div>
            </NGridItem>
            <NGridItem>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span>总内存:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.memory.total) }}</span>
                </div>
                <div class="flex justify-between">
                  <span>已使用:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.memory.used) }}</span>
                </div>
                <div class="flex justify-between">
                  <span>可用:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.memory.available) }}</span>
                </div>
                <div class="flex justify-between">
                  <span>缓存:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.memory.cached) }}</span>
                </div>
              </div>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 程序自身资源监控 -->
        <div v-if="systemData.application" class="bg-blue-50 p-4 rounded-lg">
          <h3 class="text-lg font-semibold mb-4 text-blue-800">
            <Icon icon="mdi:application" class="mr-2" />
            {{ systemData.application.name }} 程序资源使用
          </h3>

          <NGrid :cols="3" :x-gap="16" :y-gap="16">
            <!-- 程序CPU使用率 -->
            <NGridItem>
              <div class="text-center">
                <div class="text-2xl font-bold mb-2 text-blue-600">{{ systemData.application.cpu_percent }}%</div>
                <NProgress
                  :percentage="systemData.application.cpu_percent"
                  :color="getAppUsageColor(systemData.application.cpu_percent)"
                  :show-indicator="false"
                  class="mb-2"
                />
                <div class="text-sm text-gray-600">程序CPU使用率</div>
              </div>
            </NGridItem>

            <!-- 程序内存使用率 -->
            <NGridItem>
              <div class="text-center">
                <div class="text-2xl font-bold mb-2 text-blue-600">{{ systemData.application.memory.percent }}%</div>
                <NProgress
                  :percentage="systemData.application.memory.percent"
                  :color="getAppUsageColor(systemData.application.memory.percent)"
                  :show-indicator="false"
                  class="mb-2"
                />
                <div class="text-sm text-gray-600">程序内存使用率</div>
              </div>
            </NGridItem>

            <!-- 程序基本信息 -->
            <NGridItem>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span>进程ID:</span>
                  <span class="font-semibold">{{ systemData.application.pid }}</span>
                </div>
                <div class="flex justify-between">
                  <span>线程数:</span>
                  <span class="font-semibold">{{ systemData.application.threads }}</span>
                </div>
                <div class="flex justify-between">
                  <span>运行时间:</span>
                  <span class="font-semibold">{{ systemData.application.uptime_formatted }}</span>
                </div>
              </div>
            </NGridItem>
          </NGrid>

          <!-- 程序内存详细信息 -->
          <div class="mt-4">
            <h4 class="font-medium mb-2 text-blue-700">内存使用详情</h4>
            <NGrid :cols="4" :x-gap="16" :y-gap="8">
              <NGridItem>
                <NStatistic
                  label="物理内存 (RSS)"
                  :value="systemData.application.memory.rss_mb"
                  suffix="MB"
                />
              </NGridItem>
              <NGridItem>
                <NStatistic
                  label="虚拟内存 (VMS)"
                  :value="systemData.application.memory.vms_mb"
                  suffix="MB"
                />
              </NGridItem>
              <NGridItem v-if="systemData.application.file_descriptors > 0">
                <NStatistic
                  label="文件描述符"
                  :value="systemData.application.file_descriptors"
                />
              </NGridItem>
              <NGridItem>
                <NStatistic
                  label="启动时间"
                  :value="formatDateTime(systemData.application.create_time)"
                />
              </NGridItem>
            </NGrid>
          </div>
        </div>

        <!-- 交换分区监控 -->
        <div v-if="systemData.swap && systemData.swap.total > 0">
          <h3 class="text-lg font-semibold mb-4">交换分区</h3>
          <NGrid :cols="2" :x-gap="16" :y-gap="16">
            <NGridItem>
              <div class="text-center">
                <div class="text-2xl font-bold mb-2">{{ systemData.swap.percent }}%</div>
                <NProgress
                  :percentage="systemData.swap.percent"
                  :color="getUsageColor(systemData.swap.percent)"
                  :show-indicator="false"
                  class="mb-2"
                />
                <div class="text-sm text-gray-600">交换分区使用率</div>
              </div>
            </NGridItem>
            <NGridItem>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span>总大小:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.swap.total) }}</span>
                </div>
                <div class="flex justify-between">
                  <span>已使用:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.swap.used) }}</span>
                </div>
                <div class="flex justify-between">
                  <span>空闲:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.swap.free) }}</span>
                </div>
              </div>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 磁盘监控 -->
        <div v-if="systemData.disk">
          <h3 class="text-lg font-semibold mb-4">磁盘使用情况</h3>
          <NGrid :cols="2" :x-gap="16" :y-gap="16">
            <NGridItem>
              <div class="text-center">
                <div class="text-2xl font-bold mb-2">{{ systemData.disk.percent }}%</div>
                <NProgress
                  :percentage="systemData.disk.percent"
                  :color="getUsageColor(systemData.disk.percent)"
                  :show-indicator="false"
                  class="mb-2"
                />
                <div class="text-sm text-gray-600">磁盘使用率</div>
              </div>
            </NGridItem>
            <NGridItem>
              <div class="space-y-2">
                <div class="flex justify-between">
                  <span>总容量:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.disk.total) }}</span>
                </div>
                <div class="flex justify-between">
                  <span>已使用:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.disk.used) }}</span>
                </div>
                <div class="flex justify-between">
                  <span>可用:</span>
                  <span class="font-semibold">{{ formatBytes(systemData.disk.free) }}</span>
                </div>
              </div>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 网络监控 -->
        <div v-if="systemData.network && networkStats">
          <h3 class="text-lg font-semibold mb-4">网络I/O统计</h3>
          <NGrid :cols="3" :x-gap="16" :y-gap="16">
            <NGridItem>
              <NStatistic
                label="发送数据"
                :value="networkStats.sentFormatted"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="接收数据"
                :value="networkStats.recvFormatted"
              />
            </NGridItem>
            <NGridItem>
              <NStatistic
                label="网络连接数"
                :value="systemData.network.connections"
              />
            </NGridItem>
          </NGrid>
        </div>

        <!-- 系统信息 -->
        <div v-if="systemData.system">
          <h3 class="text-lg font-semibold mb-4">系统信息</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <div class="flex justify-between">
                <span>系统平台:</span>
                <span class="font-semibold">{{ systemData.system.platform }}</span>
              </div>
              <div class="flex justify-between">
                <span>系统版本:</span>
                <span class="font-semibold">{{ systemData.system.platform_release }}</span>
              </div>
              <div class="flex justify-between">
                <span>架构:</span>
                <span class="font-semibold">{{ systemData.system.architecture }}</span>
              </div>
            </div>
            <div class="space-y-2">
              <div class="flex justify-between">
                <span>进程数:</span>
                <span class="font-semibold">{{ systemData.system.process_count }}</span>
              </div>
              <div class="flex justify-between">
                <span>运行时间:</span>
                <span class="font-semibold">{{ formatUptime(systemData.system.uptime_seconds) }}</span>
              </div>
              <div class="flex justify-between">
                <span>启动时间:</span>
                <span class="font-semibold">{{ new Date(systemData.system.boot_time).toLocaleString() }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 更新时间 -->
        <div v-if="systemData.timestamp" class="text-sm text-gray-500 text-center">
          最后更新: {{ new Date(systemData.timestamp).toLocaleString() }}
        </div>
      </div>

      <!-- 系统资源监控不可用 -->
      <div v-else-if="systemData && systemData.error" class="text-center py-8">
        <NAlert type="warning" :title="systemData.error" />
        <p class="text-gray-600 mt-4">
          系统资源监控功能需要安装psutil依赖包。<br>
          请运行: <code class="bg-gray-100 px-2 py-1 rounded">pip install psutil</code>
        </p>
      </div>
    </NSpin>
  </NCard>
</template>

<style scoped>
.monitor-card {
  height: 100%;
}

.chart-card {
  height: 300px;
}

.chart-container {
  width: 100%;
  height: 260px;
}

code {
  font-family: 'Courier New', monospace;
}
</style>
