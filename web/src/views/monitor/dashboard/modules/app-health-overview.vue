<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { NCard, NGrid, NGridItem, NStatistic, NTag, NSpin, NA<PERSON>t, NProgress } from 'naive-ui';
import { fetchAppHealth } from '@/service/api';
import { useEcharts } from '@/hooks/common/echarts';

// 应用健康状态数据
const loading = ref(true);
const error = ref<string | null>(null);
const healthData = ref<Api.Monitor.AppHealthData | null>(null);

// 现代化健康度仪表盘
const { domRef: healthGaugeRef, updateOptions: updateHealthGauge } = useEcharts(() => ({
  backgroundColor: 'transparent',
  series: [{
    name: '系统健康度',
    type: 'gauge',
    center: ['50%', '55%'],
    radius: '85%',
    startAngle: 225,
    endAngle: -45,
    min: 0,
    max: 100,
    splitNumber: 5,
    itemStyle: {
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 1, y2: 0,
        colorStops: [
          { offset: 0, color: '#ff4d4f' },
          { offset: 0.3, color: '#faad14' },
          { offset: 0.7, color: '#1890ff' },
          { offset: 1, color: '#52c41a' }
        ]
      },
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowBlur: 15,
      shadowOffsetY: 3
    },
    progress: {
      show: true,
      overlap: false,
      roundCap: true,
      clip: false,
      itemStyle: {
        borderWidth: 2,
        borderColor: '#fff'
      }
    },
    axisLine: {
      lineStyle: {
        width: 8,
        color: [[1, 'rgba(0, 0, 0, 0.1)']]
      }
    },
    splitLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      show: false
    },
    pointer: {
      show: false
    },
    title: {
      show: false
    },
    detail: {
      width: 120,
      height: 60,
      fontSize: 28,
      fontWeight: 'bold',
      borderRadius: 12,
      offsetCenter: [0, '20%'],
      valueAnimation: true,
      formatter: function (value: number) {
        return `{value|${Math.round(value)}}{unit|%}`;
      },
      rich: {
        value: {
          fontSize: 32,
          fontWeight: 'bold',
          color: '#333'
        },
        unit: {
          fontSize: 18,
          color: '#666',
          padding: [0, 0, 0, 4]
        }
      }
    },
    data: [{ value: 0 }]
  }]
}));

// 组件状态环形图
const { domRef: componentStatusRef, updateOptions: updateComponentStatus } = useEcharts(() => ({
  backgroundColor: 'transparent',
  tooltip: {
    trigger: 'item',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderColor: 'transparent',
    textStyle: {
      color: '#fff',
      fontSize: 12
    },
    formatter: '{b}: {c}个组件 ({d}%)'
  },
  legend: {
    bottom: '8%',
    left: 'center',
    itemGap: 20,
    textStyle: {
      fontSize: 12,
      color: '#666'
    },
    icon: 'circle'
  },
  series: [{
    name: '组件状态',
    type: 'pie',
    radius: ['45%', '75%'],
    center: ['50%', '42%'],
    avoidLabelOverlap: false,
    itemStyle: {
      borderRadius: 6,
      borderColor: '#fff',
      borderWidth: 3,
      shadowBlur: 8,
      shadowColor: 'rgba(0, 0, 0, 0.1)'
    },
    label: {
      show: false,
      position: 'center'
    },
    emphasis: {
      label: {
        show: true,
        fontSize: '14',
        fontWeight: 'bold',
        color: '#333'
      },
      itemStyle: {
        shadowBlur: 15,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.3)'
      }
    },
    labelLine: {
      show: false
    },
    data: [] as Array<{ name: string; value: number; itemStyle: { color: string } }>
  }]
}));

onMounted(async () => {
  await loadHealthData();
});

async function loadHealthData() {
  try {
    loading.value = true;
    error.value = null;

    // 调用监控API获取应用健康状态
    const { data } = await fetchAppHealth();
    healthData.value = data;

    // 更新图表数据
    updateCharts();
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载健康数据失败';
    console.error('加载应用健康数据失败:', err);
  } finally {
    loading.value = false;
  }
}

// 监听数据变化，更新图表
watch(healthData, () => {
  updateCharts();
}, { deep: true });

function updateCharts() {
  if (!healthData.value) return;

  try {
    // 计算整体健康分数
    const healthScore = calculateHealthScore();

    // 验证健康分数有效性
    if (typeof healthScore === 'number' && healthScore >= 0 && healthScore <= 100) {
      // 更新健康状态仪表盘
      updateHealthGauge(prev => ({
        ...prev,
        series: [{
          ...prev.series[0],
          data: [{ value: healthScore }],
          itemStyle: {
            ...prev.series[0].itemStyle,
            color: getHealthGradientColor(healthScore)
          },
          progress: {
            ...prev.series[0].progress,
            itemStyle: {
              ...prev.series[0].progress.itemStyle,
              color: getHealthGradientColor(healthScore)
            }
          }
        }]
      }));
    }

    // 更新组件状态分布环形图
    if (healthData.value.database && healthData.value.strm_tasks && healthData.value.api_performance) {
      const healthyCount = [
        healthData.value.database.status === 'connected',
        healthData.value.strm_tasks.status === 'healthy',
        healthData.value.api_performance.status === 'healthy'
      ].filter(Boolean).length;

      const warningCount = [
        healthData.value.strm_tasks.status === 'warning',
        healthData.value.api_performance.status === 'warning'
      ].filter(Boolean).length;

      const errorCount = 3 - healthyCount - warningCount;

      const componentData = [];
      if (healthyCount > 0) {
        componentData.push({
          name: '健康',
          value: healthyCount,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 1,
              colorStops: [
                { offset: 0, color: '#73d13d' },
                { offset: 1, color: '#52c41a' }
              ]
            }
          }
        });
      }
      if (warningCount > 0) {
        componentData.push({
          name: '警告',
          value: warningCount,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 1,
              colorStops: [
                { offset: 0, color: '#ffd666' },
                { offset: 1, color: '#faad14' }
              ]
            }
          }
        });
      }
      if (errorCount > 0) {
        componentData.push({
          name: '异常',
          value: errorCount,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 1, y2: 1,
              colorStops: [
                { offset: 0, color: '#ff7875' },
                { offset: 1, color: '#ff4d4f' }
              ]
            }
          }
        });
      }

      updateComponentStatus(prev => ({
        ...prev,
        series: [{ ...prev.series[0], data: componentData }]
      }));
    }
  } catch (error) {
    console.error('Error updating health charts:', error);
  }
}

function calculateHealthScore(): number {
  if (!healthData.value) return 0;

  let score = 0;
  let components = 0;

  // 数据库状态 (30%)
  if (healthData.value.database.status === 'connected') {
    score += 30;
  }
  components++;

  // STRM任务状态 (35%)
  const strmStatus = healthData.value.strm_tasks.status;
  if (strmStatus === 'healthy') score += 35;
  else if (strmStatus === 'warning') score += 20;
  components++;

  // API性能状态 (35%)
  const apiStatus = healthData.value.api_performance.status;
  if (apiStatus === 'healthy') score += 35;
  else if (apiStatus === 'warning') score += 20;
  components++;

  return Math.min(100, score);
}

function getHealthColor(score: number): string {
  if (score >= 80) return '#52c41a';
  if (score >= 60) return '#1890ff';
  if (score >= 40) return '#faad14';
  return '#ff4d4f';
}

function getHealthGradientColor(score: number) {
  if (score >= 80) {
    return {
      type: 'linear',
      x: 0, y: 0, x2: 1, y2: 0,
      colorStops: [
        { offset: 0, color: '#73d13d' },
        { offset: 1, color: '#52c41a' }
      ]
    };
  } else if (score >= 60) {
    return {
      type: 'linear',
      x: 0, y: 0, x2: 1, y2: 0,
      colorStops: [
        { offset: 0, color: '#40a9ff' },
        { offset: 1, color: '#1890ff' }
      ]
    };
  } else if (score >= 40) {
    return {
      type: 'linear',
      x: 0, y: 0, x2: 1, y2: 0,
      colorStops: [
        { offset: 0, color: '#ffd666' },
        { offset: 1, color: '#faad14' }
      ]
    };
  } else {
    return {
      type: 'linear',
      x: 0, y: 0, x2: 1, y2: 0,
      colorStops: [
        { offset: 0, color: '#ff7875' },
        { offset: 1, color: '#ff4d4f' }
      ]
    };
  }
}

function getStatusColor(status: string): string {
  switch (status) {
    case 'healthy': return '#52c41a';
    case 'warning': return '#faad14';
    case 'error': return '#ff4d4f';
    default: return '#d9d9d9';
  }
}

// 外部数据更新方法
function updateData(newData: Api.Monitor.AppHealthData) {
  healthData.value = newData;
  error.value = null;
}

// 暴露方法供父组件调用
defineExpose({
  loadHealthData,
  updateData
});

function getStatusType(status: string) {
  switch (status) {
    case 'healthy':
      return 'success';
    case 'warning':
      return 'warning';
    case 'error':
      return 'error';
    default:
      return 'default';
  }
}

function formatUptime(seconds: number) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return `${days}天 ${hours}小时 ${minutes}分钟`;
}

function formatBytes(bytes: number) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getHealthStatusText(score: number): string {
  if (score >= 90) return '优秀';
  if (score >= 80) return '健康';
  if (score >= 60) return '良好';
  if (score >= 40) return '警告';
  return '故障';
}

function getHealthStatusClass(score: number): string {
  if (score >= 90) return 'status-excellent';
  if (score >= 80) return 'status-healthy';
  if (score >= 60) return 'status-good';
  if (score >= 40) return 'status-warning';
  return 'status-error';
}

function getStatusText(status: string): string {
  switch (status) {
    case 'healthy': return '健康';
    case 'warning': return '警告';
    case 'error': return '异常';
    default: return '未知';
  }
}

function getStatusClass(status: string): string {
  switch (status) {
    case 'healthy': return 'status-healthy';
    case 'warning': return 'status-warning';
    case 'error': return 'status-error';
    default: return 'status-unknown';
  }
}

function getComponentSummary(): string {
  if (!healthData.value) return '';

  const components = [
    healthData.value.database.status === 'connected',
    healthData.value.strm_tasks.status === 'healthy',
    healthData.value.api_performance.status === 'healthy'
  ];

  const healthyCount = components.filter(Boolean).length;
  return `${healthyCount}/3 组件正常运行`;
}
</script>

<template>
  <div class="health-overview-container">
    <NSpin :show="loading">
      <div v-if="error" class="mb-4">
        <NAlert type="error" :title="error" closable @close="error = null" />
      </div>

      <div v-if="healthData" class="health-content">
        <!-- 现代化健康状态仪表盘 -->
        <div class="health-dashboard">
          <NGrid :cols="2" :x-gap="24" :y-gap="24">
            <!-- 主要健康度仪表盘 -->
            <NGridItem>
              <div class="health-gauge-card">
                <div class="gauge-header">
                  <h3 class="gauge-title">系统健康度</h3>
                  <div class="health-status-badge" :class="getHealthStatusClass(calculateHealthScore())">
                    {{ getHealthStatusText(calculateHealthScore()) }}
                  </div>
                </div>
                <div ref="healthGaugeRef" class="gauge-container"></div>
                <div class="health-metrics">
                  <div class="metric-item">
                    <span class="metric-label">运行时长</span>
                    <span class="metric-value">{{ formatUptime(healthData.app_info.uptime) }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">系统版本</span>
                    <span class="metric-value">{{ healthData.app_info.version }}</span>
                  </div>
                </div>
              </div>
            </NGridItem>

            <!-- 组件状态分布 -->
            <NGridItem>
              <div class="component-status-card">
                <div class="component-header">
                  <h3 class="component-title">组件状态分布</h3>
                  <div class="component-summary">
                    {{ getComponentSummary() }}
                  </div>
                </div>
                <div ref="componentStatusRef" class="component-chart"></div>
              </div>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 详细组件状态卡片 -->
        <div class="component-details">
          <NGrid :cols="3" :x-gap="20" :y-gap="20">
            <!-- 数据库状态 -->
            <NGridItem>
              <div class="detail-card database-card">
                <div class="card-header">
                  <div class="card-icon database-icon">
                    <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
                      <path d="M12 3C7.58 3 4 4.79 4 7s3.58 4 8 4 8-1.79 8-4-3.58-4-8-4zM4 9v3c0 2.21 3.58 4 8 4s8-1.79 8-4V9c0 2.21-3.58 4-8 4s-8-1.79-8-4zM4 16v3c0 2.21 3.58 4 8 4s8-1.79 8-4v-3c0 2.21-3.58 4-8 4s-8-1.79-8-4z"/>
                    </svg>
                  </div>
                  <div class="card-title">数据库</div>
                  <div class="card-status" :class="healthData.database.status === 'connected' ? 'status-healthy' : 'status-error'">
                    {{ healthData.database.status === 'connected' ? '已连接' : '断开' }}
                  </div>
                </div>
                <div class="card-metrics">
                  <div class="metric-row">
                    <span class="metric-label">数据库大小</span>
                    <span class="metric-value">{{ formatBytes(healthData.database.size) }}</span>
                  </div>
                  <div class="metric-row">
                    <span class="metric-label">连接池</span>
                    <span class="metric-value">{{ healthData.database.connection_pool.active }}/{{ healthData.database.connection_pool.total }}</span>
                  </div>
                  <div class="connection-progress">
                    <NProgress
                      type="line"
                      :percentage="(healthData.database.connection_pool.active / healthData.database.connection_pool.total) * 100"
                      :show-indicator="false"
                      :height="6"
                      border-radius="3px"
                      fill-border-radius="3px"
                    />
                  </div>
                </div>
              </div>
            </NGridItem>

            <!-- STRM任务状态 -->
            <NGridItem>
              <div class="detail-card strm-card">
                <div class="card-header">
                  <div class="card-icon strm-icon">
                    <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
                      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                  </div>
                  <div class="card-title">STRM任务</div>
                  <div class="card-status" :class="getStatusClass(healthData.strm_tasks.status)">
                    {{ getStatusText(healthData.strm_tasks.status) }}
                  </div>
                </div>
                <div class="card-metrics">
                  <div class="metric-row">
                    <span class="metric-label">总任务数</span>
                    <span class="metric-value">{{ healthData.strm_tasks.total_tasks }}</span>
                  </div>
                  <div class="metric-row">
                    <span class="metric-label">运行中</span>
                    <span class="metric-value">{{ healthData.strm_tasks.running_tasks }}</span>
                  </div>
                  <div class="metric-row">
                    <span class="metric-label">成功率</span>
                    <span class="metric-value success-rate">{{ (healthData.strm_tasks.success_rate * 100).toFixed(1) }}%</span>
                  </div>
                  <div class="success-progress">
                    <NProgress
                      type="line"
                      :percentage="healthData.strm_tasks.success_rate * 100"
                      :show-indicator="false"
                      :height="6"
                      border-radius="3px"
                      fill-border-radius="3px"
                    />
                  </div>
                </div>
              </div>
            </NGridItem>

            <!-- API性能状态 -->
            <NGridItem>
              <div class="detail-card api-card">
                <div class="card-header">
                  <div class="card-icon api-icon">
                    <svg viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
                      <path d="M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z"/>
                    </svg>
                  </div>
                  <div class="card-title">API性能</div>
                  <div class="card-status" :class="getStatusClass(healthData.api_performance.status)">
                    {{ getStatusText(healthData.api_performance.status) }}
                  </div>
                </div>
                <div class="card-metrics">
                  <div class="metric-row">
                    <span class="metric-label">平均响应时间</span>
                    <span class="metric-value">{{ healthData.api_performance.avg_response_time.toFixed(2) }}s</span>
                  </div>
                  <div class="metric-row">
                    <span class="metric-label">24h请求数</span>
                    <span class="metric-value">{{ healthData.api_performance.request_count_24h }}</span>
                  </div>
                  <div class="metric-row">
                    <span class="metric-label">错误率</span>
                    <span class="metric-value error-rate">{{ (healthData.api_performance.error_rate * 100).toFixed(2) }}%</span>
                  </div>
                  <div class="error-progress">
                    <NProgress
                      type="line"
                      :percentage="Math.min(healthData.api_performance.error_rate * 100, 100)"
                      :show-indicator="false"
                      :height="6"
                      border-radius="3px"
                      fill-border-radius="3px"
                      status="error"
                    />
                  </div>
                </div>
              </div>
            </NGridItem>
          </NGrid>
        </div>
      </div>

      <div v-else-if="!loading" class="empty-state">
        <div class="empty-icon">
          <svg viewBox="0 0 24 24" width="48" height="48" fill="currentColor">
            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
          </svg>
        </div>
        <div class="empty-text">暂无健康状态数据</div>
      </div>
    </NSpin>
  </div>
</template>

<style scoped>
.health-overview-container {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 16px;
  min-height: 600px;
}

.health-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.health-dashboard {
  margin-bottom: 24px;
}

/* 健康度仪表盘卡片 */
.health-gauge-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  padding: 24px;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.1),
    0 1px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.health-gauge-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #52c41a, #1890ff, #faad14, #ff4d4f);
  border-radius: 20px 20px 0 0;
}

.gauge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.gauge-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.health-status-badge {
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-excellent {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: white;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

.status-healthy {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: white;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

.status-good {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.status-warning {
  background: linear-gradient(135deg, #faad14, #ffd666);
  color: white;
  box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
}

.status-error {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  color: white;
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
}

.gauge-container {
  width: 100%;
  height: 280px;
  margin: 20px 0;
}

.health-metrics {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}

.metric-item {
  text-align: center;
}

.metric-label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.metric-value {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

/* 组件状态分布卡片 */
.component-status-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  padding: 24px;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.1),
    0 1px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.component-status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff4d4f, #faad14, #52c41a);
  border-radius: 20px 20px 0 0;
}

.component-header {
  margin-bottom: 20px;
}

.component-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.component-summary {
  font-size: 14px;
  color: #6b7280;
}

.component-chart {
  width: 100%;
  height: 280px;
}

/* 详细组件状态卡片 */
.component-details {
  margin-top: 32px;
}

.detail-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  padding: 20px;
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.08),
    0 1px 6px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.detail-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 12px 35px rgba(0, 0, 0, 0.12),
    0 2px 10px rgba(0, 0, 0, 0.08);
}

.database-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #52c41a, #73d13d);
  border-radius: 16px 16px 0 0;
}

.strm-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  border-radius: 16px 16px 0 0;
}

.api-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #722ed1, #9254de);
  border-radius: 16px 16px 0 0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.database-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.strm-icon {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.api-icon {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}

.card-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-unknown {
  background: linear-gradient(135deg, #d9d9d9, #f0f0f0);
  color: #666;
}

.card-metrics {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.metric-row:last-child {
  border-bottom: none;
}

.metric-row .metric-label {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}

.metric-row .metric-value {
  font-size: 13px;
  font-weight: 600;
  color: #1f2937;
}

.success-rate {
  color: #52c41a !important;
}

.error-rate {
  color: #ff4d4f !important;
}

.connection-progress,
.success-progress,
.error-progress {
  margin-top: 8px;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #9ca3af;
}

.empty-icon {
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .health-overview-container {
    padding: 16px;
  }

  .health-content {
    gap: 24px;
  }

  .health-gauge-card,
  .component-status-card {
    padding: 16px;
  }

  .gauge-container,
  .component-chart {
    height: 240px;
  }

  .detail-card {
    padding: 16px;
  }
}
</style>
