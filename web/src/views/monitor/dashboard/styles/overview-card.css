/* 监控概览卡片统一样式 */

.overview-card {
  height: 100%;
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.overview-card .n-card-header {
  padding: 16px 20px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.overview-card .n-card__content {
  padding: 20px;
}

.overview-card .n-statistic-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

.overview-card .n-statistic-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.overview-card .quick-action {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
}

.overview-card .status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

.overview-card .metric-grid {
  display: grid;
  gap: 16px;
}

.overview-card .metric-item {
  text-align: center;
  padding: 12px;
  border-radius: 8px;
  background: #f9fafb;
  transition: background-color 0.2s ease;
}

.overview-card .metric-item:hover {
  background: #f3f4f6;
}

.overview-card .progress-section {
  margin: 16px 0;
}

.overview-card .progress-label {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.overview-card .trend-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.overview-card .trend-up {
  color: #dc2626;
  background: #fef2f2;
}

.overview-card .trend-down {
  color: #059669;
  background: #f0fdf4;
}

.overview-card .trend-stable {
  color: #6b7280;
  background: #f9fafb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overview-card .metric-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .overview-card .n-statistic-value {
    font-size: 1.25rem;
  }
  
  .overview-card .n-card__content {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .overview-card .metric-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .overview-card .metric-item {
    padding: 8px;
  }
}

/* 加载状态样式 */
.overview-card.loading {
  opacity: 0.7;
  pointer-events: none;
}

.overview-card .skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 错误状态样式 */
.overview-card .error-state {
  text-align: center;
  padding: 20px;
  color: #6b7280;
}

.overview-card .error-state .error-icon {
  font-size: 2rem;
  color: #ef4444;
  margin-bottom: 8px;
}

.overview-card .error-state .error-message {
  font-size: 0.875rem;
  margin-bottom: 12px;
}

/* 成功状态颜色 */
.status-success {
  color: #059669;
  background: #f0fdf4;
}

/* 警告状态颜色 */
.status-warning {
  color: #d97706;
  background: #fffbeb;
}

/* 错误状态颜色 */
.status-error {
  color: #dc2626;
  background: #fef2f2;
}

/* 信息状态颜色 */
.status-info {
  color: #2563eb;
  background: #eff6ff;
}
