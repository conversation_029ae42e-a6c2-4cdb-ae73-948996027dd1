<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { NCard, NGrid, NGridItem, NSpin, NAlert, NBreadcrumb, NBreadcrumbItem } from 'naive-ui';
import { useRouter } from 'vue-router';
import BusinessStats from '../dashboard/modules/business-stats.vue';

const router = useRouter();
const loading = ref(false);
const error = ref<string | null>(null);

// 组件引用
const businessStatsRef = ref();

onMounted(async () => {
  try {
    loading.value = true;
    // 加载业务统计详情数据
    if (businessStatsRef.value?.loadBusinessData) {
      await businessStatsRef.value.loadBusinessData();
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载业务统计详情失败';
  } finally {
    loading.value = false;
  }
});

onUnmounted(() => {
  // 清理资源
});

function goBack() {
  router.push('/monitor/dashboard');
}
</script>

<template>
  <div class="monitor-business">
    <!-- 面包屑导航 -->
    <div class="mb-6">
      <NBreadcrumb>
        <NBreadcrumbItem @click="goBack" class="cursor-pointer">
          <span class="text-blue-600 hover:text-blue-800">系统监控</span>
        </NBreadcrumbItem>
        <NBreadcrumbItem>业务统计</NBreadcrumbItem>
      </NBreadcrumb>
    </div>

    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">业务统计详情</h1>
        <p class="text-gray-600 mt-1">查看用户活动和业务指标详细统计</p>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mb-6">
      <NAlert type="error" :title="error" closable @close="error = null" />
    </div>

    <!-- 业务统计详情内容 -->
    <NSpin :show="loading">
      <NGrid :cols="24" :x-gap="16" :y-gap="16">
        <!-- 业务统计详情 -->
        <NGridItem :span="24">
          <NCard title="业务统计详细报告" class="mb-4">
            <BusinessStats ref="businessStatsRef" />
          </NCard>
        </NGridItem>

        <!-- 可以在这里添加更多业务相关的详细信息 -->
        <!-- 例如：用户活动趋势、业务指标图表等 -->
      </NGrid>
    </NSpin>
  </div>
</template>

<style scoped>
.monitor-business {
  padding: 0;
}
</style>
