<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { NCard, NGrid, NGridItem, NSpin, NAlert, NBreadcrumb, NBreadcrumbItem } from 'naive-ui';
import { useRouter } from 'vue-router';
import ErrorAnalysis from '../dashboard/modules/error-analysis.vue';

const router = useRouter();
const loading = ref(false);
const error = ref<string | null>(null);

// 组件引用
const errorAnalysisRef = ref();

onMounted(async () => {
  try {
    loading.value = true;
    // 加载错误分析详情数据
    if (errorAnalysisRef.value?.loadErrorData) {
      await errorAnalysisRef.value.loadErrorData();
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载错误分析详情失败';
  } finally {
    loading.value = false;
  }
});

onUnmounted(() => {
  // 清理资源
});

function goBack() {
  router.push('/monitor/dashboard');
}
</script>

<template>
  <div class="monitor-errors">
    <!-- 面包屑导航 -->
    <div class="mb-6">
      <NBreadcrumb>
        <NBreadcrumbItem @click="goBack" class="cursor-pointer">
          <span class="text-blue-600 hover:text-blue-800">系统监控</span>
        </NBreadcrumbItem>
        <NBreadcrumbItem>错误分析</NBreadcrumbItem>
      </NBreadcrumb>
    </div>

    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">错误分析详情</h1>
        <p class="text-gray-600 mt-1">查看详细错误分析和错误趋势统计</p>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mb-6">
      <NAlert type="error" :title="error" closable @close="error = null" />
    </div>

    <!-- 错误分析详情内容 -->
    <NSpin :show="loading">
      <NGrid :cols="24" :x-gap="16" :y-gap="16">
        <!-- 错误分析详情 -->
        <NGridItem :span="24">
          <NCard title="错误分析详细报告" class="mb-4">
            <ErrorAnalysis ref="errorAnalysisRef" />
          </NCard>
        </NGridItem>

        <!-- 可以在这里添加更多错误相关的详细信息 -->
        <!-- 例如：错误趋势图表、错误日志等 -->
      </NGrid>
    </NSpin>
  </div>
</template>

<style scoped>
.monitor-errors {
  padding: 0;
}
</style>
