<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { NCard, NGrid, NGridItem, NSpin, NAlert, NBreadcrumb, NBreadcrumbItem } from 'naive-ui';
import { useRouter } from 'vue-router';
import AppHealthOverview from '../dashboard/modules/app-health-overview.vue';
import SystemResources from '../dashboard/modules/system-resources.vue';

const router = useRouter();
const loading = ref(false);
const error = ref<string | null>(null);

// 组件引用
const appHealthRef = ref();
const systemResourcesRef = ref();

onMounted(async () => {
  try {
    loading.value = true;
    // 加载系统详情数据
    if (appHealthRef.value?.loadHealthData) {
      await appHealthRef.value.loadHealthData();
    }
    if (systemResourcesRef.value?.loadSystemData) {
      await systemResourcesRef.value.loadSystemData();
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载系统详情失败';
  } finally {
    loading.value = false;
  }
});

onUnmounted(() => {
  // 清理资源
});

function goBack() {
  router.push('/monitor/dashboard');
}
</script>

<template>
  <div class="monitor-system">
    <!-- 面包屑导航 -->
    <div class="mb-6">
      <NBreadcrumb>
        <NBreadcrumbItem @click="goBack" class="cursor-pointer">
          <span class="text-blue-600 hover:text-blue-800">系统监控</span>
        </NBreadcrumbItem>
        <NBreadcrumbItem>系统详情</NBreadcrumbItem>
      </NBreadcrumb>
    </div>

    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">系统详情</h1>
        <p class="text-gray-600 mt-1">查看应用健康状态和系统资源详细信息</p>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mb-6">
      <NAlert type="error" :title="error" closable @close="error = null" />
    </div>

    <!-- 系统详情内容 -->
    <NSpin :show="loading">
      <NGrid :cols="24" :x-gap="16" :y-gap="16">
        <!-- 应用健康状态详情 -->
        <NGridItem :span="24">
          <NCard title="应用健康状态" class="mb-4">
            <AppHealthOverview ref="appHealthRef" />
          </NCard>
        </NGridItem>

        <!-- 系统资源详情 -->
        <NGridItem :span="24">
          <NCard title="系统资源监控" class="mb-4">
            <SystemResources ref="systemResourcesRef" />
          </NCard>
        </NGridItem>
      </NGrid>
    </NSpin>
  </div>
</template>

<style scoped>
.monitor-system {
  padding: 0;
}
</style>
