<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { NCard, NGrid, NGridItem, NSpin, NAlert, NBreadcrumb, NBreadcrumbItem } from 'naive-ui';
import { useRouter } from 'vue-router';
import StrmTaskMonitor from '../dashboard/modules/strm-task-monitor.vue';

const router = useRouter();
const loading = ref(false);
const error = ref<string | null>(null);

// 组件引用
const strmTaskMonitorRef = ref();

onMounted(async () => {
  try {
    loading.value = true;
    // 加载任务监控详情数据
    if (strmTaskMonitorRef.value?.loadTaskStats) {
      await strmTaskMonitorRef.value.loadTaskStats();
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载任务监控详情失败';
  } finally {
    loading.value = false;
  }
});

onUnmounted(() => {
  // 清理资源
});

function goBack() {
  router.push('/monitor/dashboard');
}
</script>

<template>
  <div class="monitor-tasks">
    <!-- 面包屑导航 -->
    <div class="mb-6">
      <NBreadcrumb>
        <NBreadcrumbItem @click="goBack" class="cursor-pointer">
          <span class="text-blue-600 hover:text-blue-800">系统监控</span>
        </NBreadcrumbItem>
        <NBreadcrumbItem>任务监控</NBreadcrumbItem>
      </NBreadcrumb>
    </div>

    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">任务监控详情</h1>
        <p class="text-gray-600 mt-1">查看STRM任务详细统计和执行状态</p>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mb-6">
      <NAlert type="error" :title="error" closable @close="error = null" />
    </div>

    <!-- 任务监控详情内容 -->
    <NSpin :show="loading">
      <NGrid :cols="24" :x-gap="16" :y-gap="16">
        <!-- STRM任务监控详情 -->
        <NGridItem :span="24">
          <NCard title="STRM任务详细统计" class="mb-4">
            <StrmTaskMonitor ref="strmTaskMonitorRef" />
          </NCard>
        </NGridItem>

        <!-- 可以在这里添加更多任务相关的详细信息 -->
        <!-- 例如：任务执行历史、任务性能分析等 -->
      </NGrid>
    </NSpin>
  </div>
</template>

<style scoped>
.monitor-tasks {
  padding: 0;
}
</style>
