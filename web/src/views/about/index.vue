<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { $t } from '@/locales';

defineOptions({
  name: 'About'
});

const showContent = ref(false);

onMounted(() => {
  setTimeout(() => {
    showContent.value = true;
  }, 100);
});

const projectInfo = computed(() => ({
  name: 'StreamForge',
  version: '1.0.0',
  description: '115 STRM管理系统',
  author: 'jiqinga',
  license: 'MIT',
  repository: 'https://github.com/jiqinga/StreamForge',
  buildTime: new Date().toLocaleDateString('zh-CN'),
  features: [
    { name: 'STRM文件生成', icon: 'mdi:file-video-outline', description: '支持批量生成STRM文件，提高媒体库管理效率' },
    { name: '任务管理', icon: 'mdi:clipboard-list-outline', description: '完善的任务队列系统，支持任务监控和管理' },
    { name: '文件上传', icon: 'mdi:cloud-upload-outline', description: '支持多种文件格式上传，拖拽上传体验' },
    { name: '历史记录', icon: 'mdi:history', description: '详细的操作历史记录，便于追踪和回溯' },
    { name: '系统设置', icon: 'mdi:cog-outline', description: '灵活的系统配置选项，满足不同使用需求' },
    { name: '用户管理', icon: 'mdi:account-group-outline', description: '完整的用户权限管理体系' }
  ],
  techStack: [
    { name: 'Vue 3', icon: 'logos:vue', description: '渐进式JavaScript框架' },
    { name: 'TypeScript', icon: 'logos:typescript-icon', description: '类型安全的JavaScript超集' },
    { name: 'Vite', icon: 'logos:vitejs', description: '下一代前端构建工具' },
    { name: 'Naive UI', icon: 'logos:naiveui', description: '现代化Vue 3组件库' },
    { name: 'Python', icon: 'logos:python', description: '强大的后端编程语言' },
    { name: 'FastAPI', icon: 'logos:fastapi', description: '现代化Python Web框架' }
  ]
}));

const stats = computed(() => [
  { label: '项目版本', value: projectInfo.value.version, icon: 'mdi:tag-outline' },
  { label: '构建时间', value: projectInfo.value.buildTime, icon: 'mdi:calendar-clock' },
  { label: '技术栈', value: `${projectInfo.value.techStack.length}+`, icon: 'mdi:code-tags' },
  { label: '功能模块', value: `${projectInfo.value.features.length}+`, icon: 'mdi:puzzle-outline' }
]);
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-20px overflow-auto pb-20px">
    <!-- 主要介绍卡片 -->
    <NCard
      :bordered="false"
      size="small"
      class="hero-card"
      :class="{ 'animate-fade-in': showContent }"
    >
      <div class="flex-col-stretch gap-16px">
        <!-- 项目标题区域 -->
        <div class="flex-y-center gap-16px">
          <div class="hero-icon">
            <SvgIcon icon="mdi:rocket-launch-outline" class="text-48px text-primary" />
          </div>
          <div class="flex-col-stretch gap-6px">
            <h1 class="hero-title">{{ projectInfo.name }}</h1>
            <p class="hero-subtitle">{{ projectInfo.description }}</p>
            <div class="flex-y-center gap-8px mt-2">
              <NTag type="primary" size="small">v{{ projectInfo.version }}</NTag>
              <NTag type="info" size="small">{{ projectInfo.license }}</NTag>
            </div>
          </div>
        </div>

        <!-- 项目介绍 -->
        <div class="project-intro">
          <p class="intro-text">
            {{ $t('page.about.introduction') }}
          </p>
        </div>

        <!-- 快速统计 -->
        <div class="stats-grid">
          <div
            v-for="(stat, index) in stats"
            :key="stat.label"
            class="stat-item"
            :style="{ animationDelay: `${index * 100}ms` }"
          >
            <div class="stat-icon">
              <SvgIcon :icon="stat.icon" class="text-20px" />
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </div>
      </div>
    </NCard>

    <!-- 技术栈卡片 -->
    <NCard
      title="🛠️ 技术栈"
      :bordered="false"
      size="small"
      class="tech-card"
      :class="{ 'animate-slide-up': showContent }"
    >
      <div class="tech-grid">
        <div
          v-for="(tech, index) in projectInfo.techStack"
          :key="tech.name"
          class="tech-item"
          :style="{ animationDelay: `${index * 150}ms` }"
        >
          <div class="tech-icon">
            <SvgIcon :icon="tech.icon" class="text-32px" />
          </div>
          <div class="tech-content">
            <h4 class="tech-name">{{ tech.name }}</h4>
            <p class="tech-description">{{ tech.description }}</p>
          </div>
        </div>
      </div>
    </NCard>

    <!-- 功能特性卡片 -->
    <NCard
      title="✨ 功能特性"
      :bordered="false"
      size="small"
      class="features-card"
      :class="{ 'animate-slide-up': showContent }"
    >
      <div class="features-grid">
        <div
          v-for="(feature, index) in projectInfo.features"
          :key="feature.name"
          class="feature-item"
          :style="{ animationDelay: `${index * 100}ms` }"
        >
          <div class="feature-icon">
            <SvgIcon :icon="feature.icon" class="text-24px text-primary" />
          </div>
          <div class="feature-content">
            <h4 class="feature-name">{{ feature.name }}</h4>
            <p class="feature-description">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </NCard>

    <!-- 项目信息卡片 -->
    <NCard
      title="📋 项目信息"
      :bordered="false"
      size="small"
      class="info-card"
      :class="{ 'animate-slide-up': showContent }"
    >
      <div class="info-content">
        <div class="info-grid">
          <a
            :href="`https://github.com/${projectInfo.author}`"
            target="_blank"
            class="info-item info-item-clickable"
          >
            <div class="info-label">
              <SvgIcon icon="mdi:account-outline" class="text-16px" />
              <span>作者</span>
            </div>
            <div class="info-value">
              <div class="info-link">
                <SvgIcon icon="mdi:github" class="text-16px" />
                <span>{{ projectInfo.author }}</span>
                <SvgIcon icon="mdi:open-in-new" class="text-12px opacity-60" />
              </div>
            </div>
          </a>

          <a
            href="https://github.com/jiqinga/StreamForge/blob/master/LICENSE"
            target="_blank"
            class="info-item info-item-clickable"
          >
            <div class="info-label">
              <SvgIcon icon="mdi:license" class="text-16px" />
              <span>许可证</span>
            </div>
            <div class="info-value">
              <div class="info-link">
                <NTag type="success" size="small">{{ projectInfo.license }}</NTag>
                <SvgIcon icon="mdi:open-in-new" class="text-12px opacity-60" />
              </div>
            </div>
          </a>

          <a
            :href="projectInfo.repository"
            target="_blank"
            class="info-item info-item-clickable"
          >
            <div class="info-label">
              <SvgIcon icon="mdi:github" class="text-16px" />
              <span>仓库地址</span>
            </div>
            <div class="info-value">
              <div class="info-link">
                <SvgIcon icon="mdi:source-repository" class="text-16px" />
                <span>查看源码</span>
                <SvgIcon icon="mdi:open-in-new" class="text-12px opacity-60" />
              </div>
            </div>
          </a>
        </div>
      </div>
    </NCard>
  </div>
</template>

<style scoped>
/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-out;
}

/* 主要卡片样式 */
.hero-card {
  background: linear-gradient(135deg, rgba(24, 160, 251, 0.05) 0%, rgba(99, 102, 241, 0.05) 100%);
  border: 1px solid rgba(24, 160, 251, 0.1);
  border-radius: 16px;
  overflow: hidden;
  position: relative;
}

.hero-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #18a0fb 0%, #6366f1 100%);
}

.hero-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #18a0fb 0%, #6366f1 100%);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(24, 160, 251, 0.3);
}

.hero-title {
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #18a0fb 0%, #6366f1 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.hero-subtitle {
  font-size: 16px;
  color: var(--text-color-2);
  margin: 0;
}

.project-intro {
  padding: 20px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  border-left: 4px solid #18a0fb;
}

.intro-text {
  font-size: 15px;
  line-height: 1.8;
  color: var(--text-color-1);
  margin: 0;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(24, 160, 251, 0.1);
  transition: all 0.3s ease;
  animation: scaleIn 0.6s ease-out both;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(24, 160, 251, 0.15);
  border-color: rgba(24, 160, 251, 0.3);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #18a0fb 0%, #6366f1 100%);
  border-radius: 10px;
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-color-1);
  line-height: 1;
}

.stat-label {
  font-size: 13px;
  color: var(--text-color-3);
  margin-top: 2px;
}

/* 技术栈卡片 */
.tech-card {
  border-radius: 16px;
  border: 1px solid rgba(99, 102, 241, 0.1);
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%);
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.tech-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  border: 1px solid rgba(99, 102, 241, 0.1);
  transition: all 0.3s ease;
  animation: slideUp 0.6s ease-out both;
}

.tech-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(99, 102, 241, 0.15);
  border-color: rgba(99, 102, 241, 0.3);
}

.tech-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.1);
}

.tech-content {
  flex: 1;
}

.tech-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color-1);
  margin: 0 0 6px 0;
}

.tech-description {
  font-size: 14px;
  color: var(--text-color-2);
  margin: 0;
  line-height: 1.5;
}

/* 功能特性卡片 */
.features-card {
  border-radius: 16px;
  border: 1px solid rgba(34, 197, 94, 0.1);
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  border: 1px solid rgba(34, 197, 94, 0.1);
  transition: all 0.3s ease;
  animation: slideUp 0.6s ease-out both;
}

.feature-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(34, 197, 94, 0.15);
  border-color: rgba(34, 197, 94, 0.3);
}

.feature-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #22c55e 0%, #3b82f6 100%);
  border-radius: 14px;
  color: white;
  flex-shrink: 0;
}

.feature-content {
  flex: 1;
}

.feature-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color-1);
  margin: 0 0 8px 0;
}

.feature-description {
  font-size: 14px;
  color: var(--text-color-2);
  margin: 0;
  line-height: 1.6;
}

/* 项目信息卡片 */
.info-card {
  border-radius: 16px;
  border: 1px solid rgba(168, 85, 247, 0.1);
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.05) 0%, rgba(236, 72, 153, 0.05) 100%);
}

.info-content {
  padding: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  border: 1px solid rgba(168, 85, 247, 0.1);
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.info-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(168, 85, 247, 0.15);
  border-color: rgba(168, 85, 247, 0.3);
}

.info-item-clickable {
  cursor: pointer;
}

.info-item-clickable:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(168, 85, 247, 0.2);
  border-color: rgba(168, 85, 247, 0.4);
}

.info-item-clickable:active {
  transform: translateY(-1px);
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-2);
}

.info-value {
  font-size: 15px;
  font-weight: 600;
  color: var(--text-color-1);
}

.info-link {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
  font-weight: 600;
  color: var(--text-color-1);
}

.repository-link {
  font-weight: 500;
  transition: all 0.3s ease;
}

.repository-link:hover {
  transform: translateX(4px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 24px;
  }

  .hero-icon {
    width: 60px;
    height: 60px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .tech-grid,
  .features-grid,
  .info-grid {
    grid-template-columns: 1fr;
  }

  .tech-item,
  .feature-item {
    padding: 16px;
  }
}

/* 深色模式适配 */
.dark .hero-card,
.dark .tech-card,
.dark .features-card,
.dark .info-card {
  background: rgba(255, 255, 255, 0.02);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark .project-intro,
.dark .stat-item,
.dark .tech-item,
.dark .feature-item,
.dark .info-item {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark .tech-icon {
  background: rgba(255, 255, 255, 0.1);
}
</style>
