/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppProvider: typeof import('./../components/common/app-provider.vue')['default']
    BetterScroll: typeof import('./../components/custom/better-scroll.vue')['default']
    ButtonIcon: typeof import('./../components/custom/button-icon.vue')['default']
    ConsoleLogViewer: typeof import('./../components/custom/console-log-viewer.vue')['default']
    CountTo: typeof import('./../components/custom/count-to.vue')['default']
    CustomIconSelect: typeof import('./../components/custom/custom-icon-select.vue')['default']
    DarkModeContainer: typeof import('./../components/common/dark-mode-container.vue')['default']
    EnhancedLogFilter: typeof import('./../components/custom/enhanced-log-filter.vue')['default']
    ExceptionBase: typeof import('./../components/common/exception-base.vue')['default']
    FileParseResult: typeof import('./../components/custom/file-parse-result.vue')['default']
    FilePreview: typeof import('./../components/strm/FilePreview.vue')['default']
    FileTreeView: typeof import('./../components/custom/file-tree-view.vue')['default']
    FullScreen: typeof import('./../components/common/full-screen.vue')['default']
    GithubLink: typeof import('./../components/custom/github-link.vue')['default']
    IconAntDesignEnterOutlined: typeof import('~icons/ant-design/enter-outlined')['default']
    IconAntDesignReloadOutlined: typeof import('~icons/ant-design/reload-outlined')['default']
    IconAntDesignSettingOutlined: typeof import('~icons/ant-design/setting-outlined')['default']
    IconCarbonPlay: typeof import('~icons/carbon/play')['default']
    IconCarbonStop: typeof import('~icons/carbon/stop')['default']
    'IconCharm:download': typeof import('~icons/charm/download')['default']
    'IconF7:circleFill': typeof import('~icons/f7/circle-fill')['default']
    'IconF7:flagCircleFill': typeof import('~icons/f7/flag-circle-fill')['default']
    'IconFe:question': typeof import('~icons/fe/question')['default']
    'IconFileIcons:microsoftExcel': typeof import('~icons/file-icons/microsoft-excel')['default']
    'IconGg:ratio': typeof import('~icons/gg/ratio')['default']
    IconGridiconsFullscreen: typeof import('~icons/gridicons/fullscreen')['default']
    IconGridiconsFullscreenExit: typeof import('~icons/gridicons/fullscreen-exit')['default']
    'IconIc:roundPlus': typeof import('~icons/ic/round-plus')['default']
    'IconIconParkOutline:equalRatio': typeof import('~icons/icon-park-outline/equal-ratio')['default']
    IconIcRoundDelete: typeof import('~icons/ic/round-delete')['default']
    IconIcRoundPlus: typeof import('~icons/ic/round-plus')['default']
    IconIcRoundRefresh: typeof import('~icons/ic/round-refresh')['default']
    IconIcRoundRemove: typeof import('~icons/ic/round-remove')['default']
    IconIcRoundSearch: typeof import('~icons/ic/round-search')['default']
    IconLocalActivity: typeof import('~icons/local/activity')['default']
    IconLocalBanner: typeof import('~icons/local/banner')['default']
    IconLocalCast: typeof import('~icons/local/cast')['default']
    IconLocalLogo: typeof import('~icons/local/logo')['default']
    'IconMaterialSymbolsLight:rotate90DegreesCcwOutlineRounded': typeof import('~icons/material-symbols-light/rotate90-degrees-ccw-outline-rounded')['default']
    'IconMdi:printer': typeof import('~icons/mdi/printer')['default']
    IconMdiAlertCircle: typeof import('~icons/mdi/alert-circle')['default']
    IconMdiArrowDownThin: typeof import('~icons/mdi/arrow-down-thin')['default']
    IconMdiArrowLeft: typeof import('~icons/mdi/arrow-left')['default']
    IconMdiArrowRight: typeof import('~icons/mdi/arrow-right')['default']
    IconMdiArrowUpThin: typeof import('~icons/mdi/arrow-up-thin')['default']
    IconMdiAutorenew: typeof import('~icons/mdi/autorenew')['default']
    IconMdiCancel: typeof import('~icons/mdi/cancel')['default']
    IconMdiDownload: typeof import('~icons/mdi/download')['default']
    IconMdiDrag: typeof import('~icons/mdi/drag')['default']
    IconMdiFileExport: typeof import('~icons/mdi/file-export')['default']
    IconMdiFormatListBulleted: typeof import('~icons/mdi/format-list-bulleted')['default']
    IconMdiHistory: typeof import('~icons/mdi/history')['default']
    IconMdiInformationOutline: typeof import('~icons/mdi/information-outline')['default']
    IconMdiKeyboardEsc: typeof import('~icons/mdi/keyboard-esc')['default']
    IconMdiKeyboardReturn: typeof import('~icons/mdi/keyboard-return')['default']
    IconMdiPlay: typeof import('~icons/mdi/play')['default']
    IconMdiRefresh: typeof import('~icons/mdi/refresh')['default']
    IconMdiRefreshOff: typeof import('~icons/mdi/refresh-off')['default']
    IconMdiSync: typeof import('~icons/mdi/sync')['default']
    IconMdiUpload: typeof import('~icons/mdi/upload')['default']
    IconMdiWifi: typeof import('~icons/mdi/wifi')['default']
    IconMdiWifiOff: typeof import('~icons/mdi/wifi-off')['default']
    'IconMingcute:zoomInLine': typeof import('~icons/mingcute/zoom-in-line')['default']
    'IconMingcute:zoomOutLine': typeof import('~icons/mingcute/zoom-out-line')['default']
    IconUilSearch: typeof import('~icons/uil/search')['default']
    LangSwitch: typeof import('./../components/common/lang-switch.vue')['default']
    LookForward: typeof import('./../components/custom/look-forward.vue')['default']
    MenuToggler: typeof import('./../components/common/menu-toggler.vue')['default']
    NAlert: typeof import('naive-ui')['NAlert']
    NBreadcrumb: typeof import('naive-ui')['NBreadcrumb']
    NBreadcrumbItem: typeof import('naive-ui')['NBreadcrumbItem']
    NButton: typeof import('naive-ui')['NButton']
    NButtonGroup: typeof import('naive-ui')['NButtonGroup']
    NCard: typeof import('naive-ui')['NCard']
    NCascader: typeof import('naive-ui')['NCascader']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NCollapse: typeof import('naive-ui')['NCollapse']
    NCollapseItem: typeof import('naive-ui')['NCollapseItem']
    NColorPicker: typeof import('naive-ui')['NColorPicker']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NDescriptions: typeof import('naive-ui')['NDescriptions']
    NDescriptionsItem: typeof import('naive-ui')['NDescriptionsItem']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDivider: typeof import('naive-ui')['NDivider']
    NDrawer: typeof import('naive-ui')['NDrawer']
    NDrawerContent: typeof import('naive-ui')['NDrawerContent']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NDynamicInput: typeof import('naive-ui')['NDynamicInput']
    NDynamicTags: typeof import('naive-ui')['NDynamicTags']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NFlex: typeof import('naive-ui')['NFlex']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NFormItemGi: typeof import('naive-ui')['NFormItemGi']
    NGi: typeof import('naive-ui')['NGi']
    NGrid: typeof import('naive-ui')['NGrid']
    NGridItem: typeof import('naive-ui')['NGridItem']
    NIcon: typeof import('naive-ui')['NIcon']
    NInput: typeof import('naive-ui')['NInput']
    NInputGroup: typeof import('naive-ui')['NInputGroup']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NList: typeof import('naive-ui')['NList']
    NListItem: typeof import('naive-ui')['NListItem']
    NLoadingBarProvider: typeof import('naive-ui')['NLoadingBarProvider']
    NMenu: typeof import('naive-ui')['NMenu']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    NPagination: typeof import('naive-ui')['NPagination']
    NPopconfirm: typeof import('naive-ui')['NPopconfirm']
    NPopover: typeof import('naive-ui')['NPopover']
    NProgress: typeof import('naive-ui')['NProgress']
    NRadio: typeof import('naive-ui')['NRadio']
    NRadioButton: typeof import('naive-ui')['NRadioButton']
    NRadioGroup: typeof import('naive-ui')['NRadioGroup']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSelect: typeof import('naive-ui')['NSelect']
    NSkeleton: typeof import('naive-ui')['NSkeleton']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NStatistic: typeof import('naive-ui')['NStatistic']
    NStep: typeof import('naive-ui')['NStep']
    NSteps: typeof import('naive-ui')['NSteps']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTab: typeof import('naive-ui')['NTab']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NText: typeof import('naive-ui')['NText']
    NThing: typeof import('naive-ui')['NThing']
    NTooltip: typeof import('naive-ui')['NTooltip']
    NTree: typeof import('naive-ui')['NTree']
    NUpload: typeof import('naive-ui')['NUpload']
    NUploadDragger: typeof import('naive-ui')['NUploadDragger']
    NVirtualList: typeof import('naive-ui')['NVirtualList']
    NWatermark: typeof import('naive-ui')['NWatermark']
    PinToggler: typeof import('./../components/common/pin-toggler.vue')['default']
    ReloadButton: typeof import('./../components/common/reload-button.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SegmentedProgressBar: typeof import('./../components/custom/segmented-progress-bar.vue')['default']
    ServerManagement: typeof import('./../components/custom/server-management.vue')['default']
    SoybeanAvatar: typeof import('./../components/custom/soybean-avatar.vue')['default']
    StrmGenerate: typeof import('./../components/custom/strm-generate.vue')['default']
    SvgIcon: typeof import('./../components/custom/svg-icon.vue')['default']
    SystemLogo: typeof import('./../components/common/system-logo.vue')['default']
    TableColumnSetting: typeof import('./../components/advanced/table-column-setting.vue')['default']
    TableHeaderOperation: typeof import('./../components/advanced/table-header-operation.vue')['default']
    TaskFileList: typeof import('./../components/strm/TaskFileList.vue')['default']
    TaskFileList_backup: typeof import('./../components/strm/TaskFileList_backup.vue')['default']
    TaskFileTreeView: typeof import('./../components/strm/TaskFileTreeView.vue')['default']
    TaskStatusDisplay: typeof import('./../components/custom/task-status-display.vue')['default']
    ThemeSchemaSwitch: typeof import('./../components/common/theme-schema-switch.vue')['default']
    WaveBg: typeof import('./../components/custom/wave-bg.vue')['default']
    WebSiteLink: typeof import('./../components/custom/web-site-link.vue')['default']
  }
}
