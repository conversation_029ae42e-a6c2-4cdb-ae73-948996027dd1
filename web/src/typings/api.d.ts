/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /**
     * enable status
     *
     * - "1": enabled
     * - "2": disabled
     */
    type EnableStatus = '1' | '2';

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy: string;
      /** record create time */
      createTime: number;
      /** record updater */
      updateBy: string;
      /** record update time */
      updateTime: number;
      /** record status */
      statusType: EnableStatus | null;
      /** record fmt create time */
      fmtCreateTime: string;
      /** record fmt updateTime time */
      fmtUpdateTime: string;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginToken {
      token: string;
      refreshToken: string;
    }

    interface UserInfo {
      userId: string;
      userName: string;
      nickName: string;
      roles: string[];
      buttons: string[];
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }

  /**
   * namespace SystemManage
   *
   * backend api module: "systemManage"
   */
  namespace SystemManage {
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /** common delete params */
    type CommonDeleteParams = { id: number | string };

    /** common batch delete params */
    type CommonBatchDeleteParams = { ids: string[] };

    type tableColumnSetting = { [tableId: string]: { [key: string]: boolean } };

    /** role */
    type Role = Common.CommonRecord<{
      /** role name */
      roleName: string;
      /** role code */
      roleCode: string;
      /** role description */
      roleDesc: string;
      /** role home */
      byRoleHomeId: number;
    }>;

    /** role add params */
    type RoleAddParams = Pick<
      Api.SystemManage.Role,
      'roleName' | 'roleCode' | 'roleDesc' | 'byRoleHomeId' | 'statusType'
    >;

    /** role update params */
    type RoleUpdateParams = CommonType.RecordNullable<Pick<Api.SystemManage.Role, 'id'>> & RoleAddParams;

    /** role search params */
    type RoleSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.Role, 'roleName' | 'roleCode' | 'statusType'> & CommonSearchParams
    >;

    /** role list */
    type RoleList = Common.PaginatingQueryRecord<Role>;

    /** role authorized */
    type RoleAuthorized = Api.SystemManage.Role & {
      byRoleMenuIds: number[];
      byRoleApiIds: number[];
      byRoleButtonIds: number[];
    };

    /** get role authorized params */
    type RoleAuthorizedParams = Pick<Api.SystemManage.RoleAuthorized, 'id'>;

    /** role authorized list */
    type RoleAuthorizedList = CommonType.RecordNullable<RoleAuthorized>;

    /** all role */
    type AllRole = Pick<Role, 'id' | 'roleName' | 'roleCode'>;

    /**
     * api method
     *
     * - "1": "GET"
     * - "2": "POST"
     * - "3": "PUT"
     * - "4": "PATCH"
     * - "5": "DELETE"
     */
    type methods = 'get' | 'post' | 'put' | 'patch' | 'delete';

    /** api */
    type Api = Common.CommonRecord<{
      /** api path */
      apiPath: string;
      /** api method */
      apiMethod: methods;
      /** api summary */
      summary: string;
      /** api tags name */
      tags: string[];
    }>;

    /** api add params */
    type ApiAddParams = Pick<Api.SystemManage.Api, 'apiPath' | 'apiMethod' | 'summary' | 'tags' | 'statusType'>;

    /** api update params */
    type ApiUpdateParams = CommonType.RecordNullable<Pick<Api.SystemManage.Api, 'id'>> & ApiAddParams;

    /** api search params */
    type ApiSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.Api, 'apiPath' | 'apiMethod' | 'summary' | 'tags' | 'statusType'> & CommonSearchParams
    >;

    /** api list */
    type ApiList = Common.PaginatingQueryRecord<Api>;

    /**
     * log type
     *
     * - "1": "ApiLog"
     * - "2": "UserLog"
     * - "3": "AdminLog"
     * - "4": "SystemLog"
     */
    type logTypes = '1' | '2' | '3' | '4';

    /**
     * api method
     *
     * - "1": "GET"
     * - "2": "POST"
     * - "3": "PUT"
     * - "4": "PATCH"
     * - "5": "DELETE"
     */
    type logDetailTypes =
      | '1101'
      | '1102'
      | '1201'
      | '1202'
      | '1203'
      | '1211'
      | '1212'
      | '1213'
      | '1301'
      | '1302'
      | '1303'
      | '1311'
      | '1312'
      | '1313'
      | '1314'
      | '1315'
      | '1401'
      | '1402'
      | '1403'
      | '1404'
      | '1411'
      | '1412'
      | '1413'
      | '1414'
      | '1415'
      | '1501'
      | '1502'
      | '1503'
      | '1504'
      | '1505'
      | '1506'
      | '1507'
      | '1511'
      | '1512'
      | '1513'
      | '1514'
      | '1515'
      | '1601'
      | '1611'
      | '1612'
      | '1613'
      | '1614'
      | '1615';

    /** log */
    type Log = Common.CommonRecord<{
      /** log type */
      logType: logTypes;
      /** log detail */
      logDetailType: logDetailTypes | null;
      /** create time */
      createTime: number;
      /** format create time */

      /** request domain */
      requestDomain: string;
      /** request path */
      requestPath: string;
      /** create time */
      responseCode: string;
      /** x-request-id */
      xRequestId: string;
      /** request params */
      requestParams: string;
      /** response data */
      responseData: string;
      /** user agent */
      userAgent: string;
      /** process time */
      processTime: string;
      /** ip address */
      ipAddress: string;

      /** by user id */
      byUser: string;
      /** user info */
      byUserInfo: User;
    }>;

    /** log add params */
    type LogAddParams = Pick<
      Api.SystemManage.Log,
      | 'logType'
      | 'logDetailType'
      | 'createTime'
      | 'byUser'
      | 'requestDomain'
      | 'requestPath'
      | 'responseCode'
      | 'xRequestId'
      | 'requestParams'
      | 'responseData'
      | 'userAgent'
      | 'processTime'
      | 'ipAddress'
    >;

    /** log update params */
    type LogUpdateParams = CommonType.RecordNullable<Pick<Api.SystemManage.Log, 'id'>> & Api.SystemManage.LogAddParams;

    /** log search params */
    type LogSearchParams = CommonType.RecordNullable<
      Pick<
        Api.SystemManage.Log,
        | 'logType'
        | 'logDetailType'
        | 'requestDomain'
        | 'requestPath'
        | 'createTime'
        | 'responseCode'
        | 'byUser'
        | 'xRequestId'
      > &
        CommonSearchParams & { timeRange: [number, number] }
    >;

    /** log list */
    type LogList = Common.PaginatingQueryRecord<Log>;

    /**
     * user gender
     *
     * - "1": "male"
     * - "2": "female"
     * - "3": "secret"
     */
    type UserGender = '1' | '2' | '3';

    /** user */
    type User = Common.CommonRecord<{
      /** user name */
      userName: string;
      /** password */
      password: string;
      /** user gender */
      userGender: UserGender | null;
      /** user nick name */
      nickName: string;
      /** user phone */
      userPhone: string;
      /** user email */
      userEmail: string;
      /** user role code collection */
      byUserRoleCodeList: string[];
    }>;

    /** user add params */
    type UserAddParams = Pick<
      Api.SystemManage.User,
      | 'userName'
      | 'password'
      | 'userGender'
      | 'nickName'
      | 'userPhone'
      | 'userEmail'
      | 'byUserRoleCodeList'
      | 'statusType'
    >;

    /** user update params */
    type UserUpdateParams = CommonType.RecordNullable<Pick<Api.SystemManage.User, 'id'> & UserAddParams>;

    /** user search params */
    type UserSearchParams = CommonType.RecordNullable<
      Pick<
        Api.SystemManage.User,
        | 'userName'
        | 'password'
        | 'userGender'
        | 'nickName'
        | 'userPhone'
        | 'userEmail'
        | 'statusType'
        | 'byUserRoleCodeList'
      > &
        CommonSearchParams
    >;

    /** user list */
    type UserList = Common.PaginatingQueryRecord<User>;

    /**
     * menu type
     *
     * - "1": directory
     * - "2": menu
     */
    type MenuType = '1' | '2';

    /** menu button */
    type MenuButton = {
      /**
       * button code
       *
       * it can be used to control the button permission
       */
      buttonCode: string;
      /** button description */
      buttonDesc: string;
    };

    /**
     * icon type
     *
     * - "1": iconify icon
     * - "2": local icon
     */
    type IconType = '1' | '2';

    /** props of elegant-router */
    type MenuPropsOfRoute = Pick<
      import('vue-router').RouteMeta,
      | 'i18nKey'
      | 'keepAlive'
      | 'constant'
      | 'order'
      | 'href'
      | 'hideInMenu'
      | 'activeMenu'
      | 'multiTab'
      | 'fixedIndexInTab'
      | 'query'
    >;

    /** menu */
    type Menu = Common.CommonRecord<{
      /** parent menu id */
      parentId: number;
      /** menu type */
      menuType: MenuType;
      /** menu name */
      menuName: string;
      /** route name */
      routeName: string;
      /** route path */
      routePath: string;
      /** component */
      component?: string;
      /** iconify icon name or local icon name */
      icon: string;
      /** icon type */
      iconType: IconType;
      /** buttons */
      buttons?: MenuButton[] | null;
      /** children menu */
      children?: Menu[] | null;
    }> &
      MenuPropsOfRoute;

    /** menu add params */
    type MenuAddParams = Pick<
      Api.SystemManage.Menu,
      | 'menuType'
      | 'menuName'
      | 'routeName'
      | 'routePath'
      | 'component'
      | 'order'
      | 'i18nKey'
      | 'icon'
      | 'iconType'
      | 'statusType'
      | 'parentId'
      | 'keepAlive'
      | 'constant'
      | 'href'
      | 'hideInMenu'
      | 'activeMenu'
      | 'multiTab'
      | 'fixedIndexInTab'
    > & {
      query: NonNullable<Api.SystemManage.Menu['query']>;
      buttons: NonNullable<Api.SystemManage.Menu['buttons']>;
      layout: string;
      page: string;
      pathParam: string;
    };

    /** menu update params */
    type MenuUpdateParams = CommonType.RecordNullable<Pick<Api.SystemManage.Menu, 'id'>> & MenuAddParams;

    /** menu list */
    type MenuList = Common.PaginatingQueryRecord<Menu>;

    /** menu tree */
    type MenuTree = {
      id: number;
      label: string;
      pId: number;
      children?: MenuTree[];
    };

    /** button tree */
    type ButtonTree = {
      id: number;
      label: string;
      pId: number;
      children?: ButtonTree[];
    };
  }
}

declare namespace StrmAPI {
  interface FileStats {
    total: number;
    video: number;
    audio: number;
    image: number;
    subtitle: number;
    metadata: number;
    other: number;
  }

  interface ParsedFile {
    path: string;
    file_type: string;
    size?: number;
    ctime?: string;
    mtime?: string;
    ext?: string;
    parent_dir?: string;
    name?: string;
  }

  interface UploadResult {
    filename: string;
    path: string;
    record_id: number;
  }

  interface DirectoryContent {
    directory_path: string;
    files: ParsedFile[];
    subdirectories: string[];
    stats: {
      file_count: number;
      subdirectory_count: number;
    };
  }

  interface ParseStats {
    total: number;
    video: number;
    audio: number;
    image: number;
    subtitle: number;
    metadata: number;
    other: number;
  }

  interface ParseResult {
    file_name: string;
    parsed_files: ParsedFile[];
    total_files: number;
    stats: ParseStats;
  }

  interface UploadRecord {
    id: number;
    filename: string;
    filesize: number;
    status: string;
    create_time: string;
    uploader_id: number;
    parse_time?: string;
  }

  interface FilterRuleResponse {
    id: number;
    create_time: string;
    update_time: string;
    name: string;
    file_type?: 'video' | 'audio' | 'image' | 'subtitle' | 'other';
    keyword?: string;
    path_pattern?: string;
    is_include: boolean;
  }

  interface MediaServer {
    id: number;
    name: string;
    server_type: string;
    base_url: string;
    is_default: boolean;
    description?: string;
    auth_required: boolean;
    create_time: string;
  }

  /**
   * Task type enum
   * - 'strm': STRM file generation task
   * - 'resource_download': Resource download task
   */
  type TaskType = 'strm' | 'resource_download';

  interface StrmTaskCreate {
    record_id: number;
    server_id: number;
    output_dir?: string;
    name?: string;
    task_type?: TaskType;
  }

  interface StrmFileDetail {
    id: number;
    source_path: string;
    target_path: string;
    file_type: string;
    file_size?: number;
    is_success: boolean;
    error_message?: string;
  }

  interface ResourceFileDetail {
    id: number;
    source_path: string;
    target_path: string;
    file_size?: number;
    is_success: boolean;
    download_progress?: number;
    error_message?: string;
  }

  interface StrmTaskDetail {
    id: number;
    name: string;
    status: string;
    task_type: TaskType;
    total_files: number;
    processed_files: number;
    success_files: number;
    failed_files: number;
    progress: number;
    start_time?: string;
    end_time?: string;
    output_dir: string;
    files: StrmFileDetail[] | ResourceFileDetail[];
    file_count: number;
    resource_files_count: number;
    error?: string;
    elapsed_time?: string;
    strm_files_count: number;
    strm_success: number;
    strm_failed: number;
    resource_success: number;
    resource_failed: number;
    video_files_count: number;
  }

  interface StrmTaskBrief {
    id: number;
    name: string;
    status: string;
    task_type: TaskType;
    total_files: number;
    processed_files: number;
    success_files: number;
    failed_files: number;
    progress: number;
    start_time?: string;
    end_time?: string;
  }

  interface StrmTaskResponse {
    total: number;
    page: number;
    page_size: number;
    tasks: StrmTaskBrief[];
  }

  interface StrmGenerateResult {
    task_id: number;
    name: string;
    status: string;
    result: {
      success: boolean;
      message: string;
      task_id: number;
      result?: any;
    };
  }

  interface ResourceDownloadTaskCreate {
    record_id: number;
    file_paths: string[];
    output_dir?: string;
    name?: string;
  }
}

declare namespace Api {
  namespace Page {
    interface PageParams {
      page?: number;
      page_size?: number;
    }

    interface PageResult<T> {
      total: number;
      page: number;
      page_size: number;
      records: T[];
    }
  }

  /**
   * namespace Monitor
   *
   * backend api module: "monitor"
   */
  namespace Monitor {
    /** 应用健康状态数据 */
    interface AppHealthData {
      app_info: {
        name: string;
        version: string;
        start_time: string;
        uptime: number;
        status: 'healthy' | 'warning' | 'error';
        platform: string;
        python_version: string;
      };
      database: {
        status: 'connected' | 'disconnected';
        size: number;
        connection_pool: {
          active: number;
          idle: number;
          total: number;
        };
        query_performance?: {
          avg_response_time: number;
          slow_queries: number;
        };
      };
      strm_tasks: {
        status: 'healthy' | 'warning' | 'error';
        total_tasks: number;
        running_tasks: number;
        failed_tasks: number;
        success_rate: number;
        recent_failures?: number;
      };
      api_performance: {
        status: 'healthy' | 'warning' | 'error';
        avg_response_time: number;
        request_count_24h: number;
        error_rate: number;
        slowest_endpoints?: Array<{
          endpoint: string;
          avg_time: number;
          count: number;
        }>;
      };
    }

    /** 业务统计数据 */
    interface BusinessStatsData {
      strm_tasks: {
        total: number;
        completed: number;
        failed: number;
        running: number;
        pending: number;
        success_rate: number;
        avg_processing_time: number;
      };
      user_activity: {
        total_users: number;
        active_today: number;
        login_count_today: number;
        active_users_week: number;
      };
      api_requests: {
        total_requests_today: number;
        avg_response_time: number;
        error_count: number;
        top_endpoints: Array<{
          endpoint: string;
          count: number;
        }>;
      };
    }

    /** 性能监控数据 */
    interface PerformanceData {
      api_performance: {
        avg_response_time: number;
        request_count: number;
        error_rate: number;
        requests_per_minute: number;
        slowest_endpoints: Array<{
          endpoint: string;
          avg_time: number;
          count: number;
          max_time: number;
          error_count?: number;
          error_rate?: number;
        }>;
        status_code_distribution: Record<string, number>;
      };
      database_performance: {
        connection_status: string;
        query_stats: {
          avg_query_time: number;
          slow_queries: number;
          total_queries: number;
        };
      };
      system_resources: {
        available: boolean;
        memory_usage?: number;
        cpu_usage?: number;
        disk_usage?: number;
        memory_total?: number;
        memory_available?: number;
        disk_total?: number;
        disk_free?: number;
        error?: string;
      };
    }

    /** 系统资源概览数据 */
    interface SystemOverviewData {
      available: boolean;
      timestamp?: string;
      cpu?: {
        percent: number;
        cores_physical: number;
        cores_logical: number;
        load_avg: number[];
        frequency?: {
          current: number;
          min: number;
          max: number;
        };
      };
      memory?: {
        total: number;
        available: number;
        used: number;
        percent: number;
        free: number;
        buffers: number;
        cached: number;
      };
      swap?: {
        total: number;
        used: number;
        free: number;
        percent: number;
      };
      disk?: {
        total: number;
        used: number;
        free: number;
        percent: number;
      };
      network?: {
        bytes_sent: number;
        bytes_recv: number;
        packets_sent: number;
        packets_recv: number;
        errin: number;
        errout: number;
        dropin: number;
        dropout: number;
        connections: number;
      };
      system?: {
        process_count: number;
        boot_time: string;
        uptime_seconds: number;
        platform: string;
        platform_release: string;
        platform_version: string;
        architecture: string;
      };
      application?: {
        name: string;
        pid: number;
        cpu_percent: number;
        memory: {
          rss: number;
          vms: number;
          percent: number;
          rss_mb: number;
          vms_mb: number;
        };
        threads: number;
        file_descriptors: number;
        uptime_seconds: number;
        uptime_formatted: string;
        create_time: string;
      };
      error?: string;
    }

    /** 错误分析数据 */
    interface ErrorAnalysisData {
      error_summary: {
        total_errors: number;
        total_requests: number;
        error_rate: number;
        top_errors: Array<{
          error_type: string;
          error_code: string;
          count: number;
          percentage: number;
        }>;
        top_error_endpoints: Array<{
          endpoint: string;
          count: number;
          methods: string[];
          error_codes: string[];
        }>;
      };
      failed_tasks: {
        total: number;
        total_tasks: number;
        failure_rate: number;
        by_type: Array<{
          type: string;
          count: number;
        }>;
      };
      trend_data: Array<{
        timestamp: string;
        api_error_count: number;
        api_error_rate: number;
        task_failure_count: number;
        total_requests: number;
      }>;
      analysis_period_hours: number;
    }

    /** API趋势数据 */
    interface ApiTrendsData {
      trend_data: Array<{
        timestamp: string;
        request_count: number;
        avg_response_time: number;
        error_count: number;
        error_rate: number;
      }>;
      interval_minutes: number;
      total_intervals: number;
    }

    /** 慢查询数据 */
    interface SlowQueriesData {
      slow_queries: Array<{
        request_path: string;
        request_method: string;
        process_time: number;
        response_code: number;
        create_time: string;
        x_request_id: string;
      }>;
    }

    /** 任务性能数据 */
    interface TaskPerformanceData {
      total_completed: number;
      avg_duration: number;
      min_duration: number;
      max_duration: number;
      fastest_task?: {
        id: number;
        name: string;
        duration: number;
        total_files: number;
        success_files: number;
        failed_files: number;
      };
      slowest_task?: {
        id: number;
        name: string;
        duration: number;
        total_files: number;
        success_files: number;
        failed_files: number;
      };
    }

    /** 缓存信息数据 */
    interface CacheInfoData {
      total_keys: number;
      expired_keys: number;
      active_keys: number;
      locks_count: number;
      cache_keys: string[];
    }

    /** STRM任务详细统计数据 */
    interface StrmTaskStatsData {
      status_distribution: {
        pending: number;
        running: number;
        completed: number;
        failed: number;
      };
      processing_stats: {
        avg_duration: number;
        min_duration: number;
        max_duration: number;
        median_duration: number;
      };
      queue_status: {
        current_running: number;
        current_pending: number;
        queue_length: number;
      };
      performance_insights: {
        fastest_task?: {
          id: number;
          name: string;
          duration: number;
          start_time: string;
          end_time: string;
          file_count: number;
        };
        slowest_task?: {
          id: number;
          name: string;
          duration: number;
          start_time: string;
          end_time: string;
          file_count: number;
        };
        total_completed: number;
        total_failed: number;
        success_rate: number;
      };
      trend_data: Array<{
        timestamp: string;
        total_tasks: number;
        completed_tasks: number;
        failed_tasks: number;
        success_rate: number;
        avg_processing_time: number;
      }>;
      recent_failures: Array<{
        id: number;
        name: string;
        error_message: string;
        create_time: string;
        file_count: number;
      }>;
      analysis_period_hours: number;
      total_tasks_analyzed: number;
    }

    /** SSE实时监控数据 */
    interface RealtimeMonitorData {
      timestamp: string;
      health: AppHealthData;
      business: BusinessStatsData;
      performance: PerformanceData;
    }

    /** SSE错误数据 */
    interface RealtimeErrorData {
      timestamp: string;
      error: string;
      type: 'monitor_error';
    }
  }
}
