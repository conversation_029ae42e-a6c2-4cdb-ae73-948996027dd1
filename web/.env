# the base url of the application, the default is "/"
# if use a sub directory, it must be end with "/", like "/admin/" but not "/admin"
VITE_BASE_URL=/

VITE_APP_TITLE=SoybeanAdmin

VITE_APP_DESC=SoybeanAdmin is a fresh and elegant admin template

# the prefix of the icon name
VITE_ICON_PREFIX=icon

# the prefix of the local svg icon component, must include VITE_ICON_PREFIX
# format {VITE_ICON_PREFIX}-{local icon name}
VITE_ICON_LOCAL_PREFIX=icon-local

# auth route mode: static ｜ dynamic
VITE_AUTH_ROUTE_MODE=dynamic

# static auth route home
VITE_ROUTE_HOME=home

# default menu icon
VITE_MENU_ICON=mdi:menu

# whether to enable http proxy when is dev mode
VITE_HTTP_PROXY=Y

# vue-router mode: hash | history | memory
VITE_ROUTER_HISTORY_MODE=history

# success code of backend service, when the code is received, the request is successful
VITE_SERVICE_SUCCESS_CODE=0000

# logout codes of backend service, when the code is received, the user will be logged out and redirected to login page
VITE_SERVICE_LOGOUT_CODES=8888,8889,4000,4040

# modal logout codes of backend service, when the code is received, the user will be logged out by displaying a modal
VITE_SERVICE_MODAL_LOGOUT_CODES=7777,7778

# token expired codes of backend service, when the code is received, it will refresh the token and resend the request
VITE_SERVICE_EXPIRED_TOKEN_CODES=9999,9998,4010

# when the route mode is static, the defined super role
VITE_STATIC_SUPER_ROLE=R_SUPER

# sourcemap
VITE_SOURCE_MAP=N

# Used to differentiate storage across different domains
VITE_STORAGE_PREFIX=SOY_

# used to control whether the program automatically detects updates
VITE_AUTOMATICALLY_DETECT_UPDATE=Y


